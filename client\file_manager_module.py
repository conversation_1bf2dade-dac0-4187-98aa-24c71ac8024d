import asyncio
import json
import logging
import os
from pathlib import Path
from datetime import datetime
import websockets


# 修改日志相关内容
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('文件浏览器-客户端')

class FileExplorerClient:
    def __init__(self, server_url: str, client_id: str, websocket=None):
        self.server_url = server_url
        self.client_id = client_id
        self.websocket = websocket

    async def get_drive_info(self):
        """获取驱动器列表"""
        drives = []
        if os.name == 'nt':  # Windows系统
            for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
                drive = f"{letter}:"
                if os.path.exists(drive):
                    try:
                        total, used, free = self._get_drive_space(drive)
                        drives.append({
                            'name': drive,
                            'total': total,
                            'free': free
                        })
                    except Exception as e:
                        logger.error(f"获取驱动器 {drive} 信息时出错: {e}")
        return drives

    async def get_directory_content(self, path: str, command: dict = None):
        """获取目录内容"""
        try:
            # 处理分区根目录的情况
            if path.endswith(':'):
                path = path + '\\'  # 添加反斜杠以访问分区根目录
                
            if not os.path.exists(path):
                logger.error(f"路径不存在: {path}")
                return {
                    'error': '路径不存在',
                    'client_id': self.client_id
                }

            items = []
            try:
                with os.scandir(path) as entries:
                    for entry in entries:
                        try:
                            stat = entry.stat()
                            items.append({
                                'name': entry.name,
                                'is_dir': entry.is_dir(),
                                'size': stat.st_size if entry.is_file() else 0,
                                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                            })
                        except Exception as e:
                            logger.error(f"获取文件 {entry.name} 信息时出错: {e}")
                            continue

                response = {
                    'type': 'file_manager_directory_content',
                    'client_id': self.client_id,
                    'path': path,
                    'items': items
                }
                
                if command:
                    response['for_nav_tree'] = command.get('for_nav_tree', False)
                    response['tree_item'] = command.get('tree_item')
                
                logger.info(f"已获取目录内容: {path}, 共 {len(items)} 个项目")
                return response
                
            except PermissionError:
                error_msg = f'访问被拒绝: {path}'
                logger.error(error_msg)
                return {
                    'error': error_msg,
                    'client_id': self.client_id
                }
                
        except Exception as e:
            error_msg = f"获取目录内容失败: {str(e)}"
            logger.error(error_msg)
            return {
                'error': error_msg,
                'client_id': self.client_id
            }

    def _get_drive_space(self, path):
        """获取驱动器空间信息"""
        try:
            if os.name == 'nt':
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                total_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(path), 
                    None,
                    ctypes.pointer(total_bytes),
                    ctypes.pointer(free_bytes)
                )
                return total_bytes.value, 0, free_bytes.value
            else:
                stats = os.statvfs(path)
                total = stats.f_blocks * stats.f_frsize
                free = stats.f_bfree * stats.f_frsize
                return total, 0, free
        except Exception as e:
            logger.error(f"获取驱动器空间信息失败: {e}")
            return 0, 0, 0

    async def handle_command(self, command: dict):
        """处理命令"""
        try:
            cmd_type = command.get('type')
            path = command.get('path', '')
            
            logger.info(f"处理文件管理器命令: {command}")
            
            if cmd_type == 'file_manager_get_directory':
                response = await self.get_directory_content(path, command)
                if self.websocket:
                    await self.websocket.send(json.dumps(response))
                return response
                
            elif cmd_type == 'file_manager_get_drives':
                drives = await self.get_drive_info()
                response = {
                    'type': 'file_manager_drives',
                    'client_id': self.client_id,
                    'drives': drives
                }
                if self.websocket:
                    await self.websocket.send(json.dumps(response))
                return response
                
            elif cmd_type == 'file_manager_delete':
                return await self.delete_item(path)
                
            elif cmd_type == 'file_manager_upload':
                source_path = command.get('source_path')
                target_path = command.get('target_path')
                return await self.upload_file(source_path, target_path)
                
            elif cmd_type == 'file_manager_download':
                source_path = command.get('source_path')
                target_path = command.get('target_path')
                return await self.download_file(source_path, target_path)
                
            elif cmd_type == 'file_manager_paste':
                source_path = command.get('source_path')
                target_path = command.get('target_path')
                action = command.get('action')
                return await self.paste_item(source_path, target_path, action)
                
            else:
                logger.warning(f"未知的命令类型: {cmd_type}")
                return {
                    'type': 'error',
                    'client_id': self.client_id,
                    'message': f'未知的命令类型: {cmd_type}'
                }
                
        except Exception as e:
            error_msg = f"处理命令时出错: {str(e)}"
            logger.error(error_msg)
            return {
                'type': 'error',
                'client_id': self.client_id,
                'message': error_msg
            }

    async def delete_item(self, path: str):
        """删除文件或目录"""
        try:
            if os.path.exists(path):
                if os.path.isdir(path):
                    import shutil
                    shutil.rmtree(path)
                else:
                    os.remove(path)
                    
                response = {
                    'type': 'file_manager_response',
                    'client_id': self.client_id,
                    'status': 'success',
                    'message': f'成功删除 {path}'
                }
                
                # 发送目录更新
                parent_dir = os.path.dirname(path)
                await self.get_directory_content(parent_dir)
                
                return response
            else:
                return {
                    'type': 'file_manager_response',
                    'client_id': self.client_id,
                    'status': 'error',
                    'message': f'文件或目录不存在: {path}'
                }
        except Exception as e:
            error_msg = f"删除失败: {str(e)}"
            logger.error(error_msg)
            return {
                'type': 'file_manager_response',
                'client_id': self.client_id,
                'status': 'error',
                'message': error_msg
            }

    async def upload_file(self, source_path: str, target_path: str):
        """上传文件"""
        try:
            # 确保目标目录存在
            os.makedirs(target_path, exist_ok=True)
            
            # 构建目标文件路径
            filename = os.path.basename(source_path)
            target_file = os.path.join(target_path, filename)
            
            # 复制文件
            import shutil
            shutil.copy2(source_path, target_file)
            
            response = {
                'type': 'file_manager_response',
                'client_id': self.client_id,
                'status': 'success',
                'message': f'文件上传成功: {filename}'
            }
            
            # 发送目录更新
            await self.get_directory_content(target_path)
            
            return response
            
        except Exception as e:
            error_msg = f"上传失败: {str(e)}"
            logger.error(error_msg)
            return {
                'type': 'file_manager_response',
                'client_id': self.client_id,
                'status': 'error',
                'message': error_msg
            }

    async def paste_item(self, source_path: str, target_path: str, action: str):
        """粘贴文件或目录"""
        try:
            import shutil
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            
            if action == 'copy':
                if os.path.isdir(source_path):
                    shutil.copytree(source_path, target_path)
                else:
                    shutil.copy2(source_path, target_path)
            elif action == 'cut':
                shutil.move(source_path, target_path)
            
            response = {
                'type': 'file_manager_response',
                'client_id': self.client_id,
                'status': 'success',
                'message': f'{"移动" if action == "cut" else "复制"}成功'
            }
            
            # 发送目录更新
            await self.get_directory_content(os.path.dirname(target_path))
            if action == 'cut':
                await self.get_directory_content(os.path.dirname(source_path))
            
            return response
            
        except Exception as e:
            error_msg = f"粘贴失败: {str(e)}"
            logger.error(error_msg)
            return {
                'type': 'file_manager_response',
                'client_id': self.client_id,
                'status': 'error',
                'message': error_msg
            }

class FileManager:
    def __init__(self):
        self.websocket = None
        self.current_path = ""
        
    async def initialize(self, websocket):
        """初始化文件管理器"""
        self.websocket = websocket
        
    async def handle_command(self, command_data):
        """处理文件管理命令"""
        try:
            command = command_data.get('command', '')
            
            if command == 'file_manager_get_drives':
                return await self.get_drives()
            elif command == 'file_manager_get_directory':
                path = command_data.get('path', '')
                return await self.get_directory_content(path)
            # ... 其他命令处理 ...
            
        except Exception as e:
            logger.error(f"处理文件管理命令失败: {e}")
            return f"处理文件管理命令失败: {e}"