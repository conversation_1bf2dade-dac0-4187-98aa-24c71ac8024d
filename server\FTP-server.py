from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, Header, HTTPException
from fastapi.responses import JSONResponse
import shutil
import os
from pathlib import Path, PureWindowsPath
import logging
import asyncio
from typing import Optional
import aiofiles
import traceback

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI()

# 上传配置
CURRENT_DIR = Path.cwd()
TEMP_DIR = CURRENT_DIR / "temp_uploads"
CHUNK_SIZE = 1024 * 1024  # 1MB 缓冲区

# 确保目录存在
#UPLOAD_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)

# 用于跟踪上传进度的字典
upload_tracker = {}

class UploadManager:
    def __init__(self, file_id: str, remote_path: str):
        self.file_id = file_id
        self.remote_path = remote_path
        self.chunks = {}
        self.temp_dir = TEMP_DIR / file_id
        self.temp_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        self.logger.debug(f"Created upload manager for file_id: {file_id}, remote_path: {remote_path}")

    async def save_chunk(self, chunk_number: int, chunk_data: bytes) -> None:
        chunk_path = self.temp_dir / f"chunk_{chunk_number}"
        self.logger.debug(f"Saving chunk {chunk_number} to {chunk_path}")
        
        try:
            async with aiofiles.open(chunk_path, 'wb') as f:
                await f.write(chunk_data)
            self.chunks[chunk_number] = True
            self.logger.debug(f"Successfully saved chunk {chunk_number}")
        except Exception as e:
            self.logger.error(f"Error saving chunk {chunk_number}: {str(e)}")
            raise

    def ensure_directory_exists(self, path: Path):
        """确保目录存在，如果不存在则创建"""
        try:
            if not path.exists():
                # 递归创建所有父目录
                for parent in reversed(path.parents):
                    if not parent.exists():
                        self.logger.info(f"Creating parent directory: {parent}")
                        parent.mkdir(parents=True, exist_ok=True)
                        os.chmod(parent, 0o755)
                        self.logger.info(f"Parent directory created successfully: {parent}")

                # 创建目标目录
                self.logger.info(f"Creating directory: {path}")
                path.mkdir(parents=True, exist_ok=True)
                os.chmod(path, 0o755)
                self.logger.info(f"Directory created successfully: {path}")
        except Exception as e:
            self.logger.error(f"Error creating directory {path}: {str(e)}")
            raise

    def get_final_path(self) -> Path:
        """获取最终的文件存储路径"""
        try:
            # 使用当前目录作为基础路径
            # remote_path 格式应该是: /uploads/hostname/drive_path
            # 例如: /uploads/PC-001/C_Users_Desktop_file.txt
            
            # 移除开头的斜杠并分割路径
            clean_path = self.remote_path.lstrip('/')
            
            # 构建完整的存储路径（相对于脚本运行目录）
            final_path = CURRENT_DIR / clean_path
            
            # 确保父目录存在
            self.ensure_directory_exists(final_path.parent)
            
            self.logger.debug(f"Original remote path: {self.remote_path}")
            self.logger.debug(f"Clean path: {clean_path}")
            self.logger.debug(f"Final storage path: {final_path}")
            
            return final_path
            
        except Exception as e:
            self.logger.error(f"Error in get_final_path: {str(e)}")
            raise


    async def merge_chunks(self, filename: str, total_chunks: int):
        try:
            final_path = self.get_final_path()
            self.logger.debug(f"Merging chunks to {final_path}")
            
            async with aiofiles.open(final_path, 'wb') as outfile:
                for i in range(total_chunks):
                    chunk_path = self.temp_dir / f"chunk_{i}"
                    if not chunk_path.exists():
                        raise HTTPException(status_code=400, detail=f"Missing chunk {i}")
                    
                    self.logger.debug(f"Reading chunk {i} from {chunk_path}")
                    async with aiofiles.open(chunk_path, 'rb') as chunk_file:
                        while True:
                            chunk = await chunk_file.read(CHUNK_SIZE)
                            if not chunk:
                                break
                            await outfile.write(chunk)
            
            # 设置文件权限为 644
            os.chmod(final_path, 0o644)
            
            # 验证文件是否成功创建
            if final_path.exists():
                self.logger.debug(f"File successfully created at {final_path}")
                self.logger.debug(f"File size: {final_path.stat().st_size} bytes")
            else:
                raise Exception(f"File was not created at {final_path}")
            
            self.logger.debug("File merge completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error merging chunks: {str(e)}\n{traceback.format_exc()}")
            raise

    async def cleanup(self):
        """清理临时文件和目录"""
        try:
            if self.temp_dir.exists():
                for chunk_file in self.temp_dir.glob("chunk_*"):
                    chunk_file.unlink()
                self.temp_dir.rmdir()
            self.logger.debug("Cleanup completed successfully")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
            raise

@app.post("/upload")
async def upload_chunk(
    file: UploadFile = File(...),
    content_range: str = Form(...),
    remote_path: str = Form(...),
    file_id: Optional[str] = Header(None)
):
    try:
        logger.debug(f"Received upload request - file_id: {file_id}")
        logger.debug(f"Remote path: {remote_path}")
        logger.debug(f"Content range: {content_range}")
        
        if not file_id:
            raise HTTPException(status_code=400, detail="Missing file_id header")
            
        if not remote_path:
            raise HTTPException(status_code=400, detail="Missing remote_path")
        
        try:
            chunk_number, total_chunks = map(int, content_range.split('/'))
        except Exception as e:
            logger.error(f"Error parsing content_range: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Invalid content_range format: {content_range}")
        
        # 获取或创建上传管理器
        if file_id not in upload_tracker:
            logger.debug(f"Creating new upload manager for file_id: {file_id}")
            upload_tracker[file_id] = UploadManager(file_id, remote_path)
        
        upload_mgr = upload_tracker[file_id]
        chunk_data = await file.read()
        
        await upload_mgr.save_chunk(chunk_number, chunk_data)
        
        if len(upload_mgr.chunks) == total_chunks:
            await upload_mgr.merge_chunks(file.filename, total_chunks)
            await upload_mgr.cleanup()
            del upload_tracker[file_id]
            
            return JSONResponse({
                "status": "success",
                "message": "File upload completed",
                "filename": file.filename,
                "remote_path": remote_path
            })
        
        return JSONResponse({
            "status": "success",
            "message": f"Chunk {chunk_number} received",
            "received_chunks": len(upload_mgr.chunks),
            "total_chunks": total_chunks
        })

    except Exception as e:
        logger.error(f"Upload error: {str(e)}\n{traceback.format_exc()}")
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(status_code=500, detail=str(e))

# 上传进度跟踪字典
upload_tracker = {}

@app.on_event("startup")
async def startup_event():
    """服务启动时的初始化"""
    try:
        logger.info("Starting server initialization")
        
        # 清理临时目录
        if TEMP_DIR.exists():
            shutil.rmtree(TEMP_DIR)
        TEMP_DIR.mkdir(exist_ok=True)
        os.chmod(TEMP_DIR, 0o755)
        
        # 确保uploads目录存在
        uploads_dir = CURRENT_DIR / "uploads"
        if not uploads_dir.exists():
            uploads_dir.mkdir(parents=True)
            os.chmod(uploads_dir, 0o755)
            
        # 添加日志目录
        log_dir = CURRENT_DIR / "logs"
        if not log_dir.exists():
            log_dir.mkdir(parents=True)
            os.chmod(log_dir, 0o755)
            
        logger.info(f"Current directory: {CURRENT_DIR}")
        logger.info(f"Upload directory: {uploads_dir}")
        logger.info(f"Temporary directory: {TEMP_DIR}")
        logger.info(f"Log directory: {log_dir}")
        logger.info("Server initialization completed")
        
    except Exception as e:
        logger.error(f"Startup error: {str(e)}")
        raise

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="debug")