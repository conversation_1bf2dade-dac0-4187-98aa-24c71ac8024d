using AvaloniaControlCenter.Models;
using AvaloniaControlCenter.Services;
using ReactiveUI;
using Serilog;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;

namespace AvaloniaControlCenter.ViewModels;

/// <summary>
/// 批量命令ViewModel
/// </summary>
public class BatchCommandViewModel : ViewModelBase
{
    private readonly ILogger _logger;
    private readonly ClientManager _clientManager;
    private string _commandText = string.Empty;
    private CommandTemplate? _selectedTemplate;
    private bool _isExecuting = false;
    private string _executionStatus = string.Empty;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="clientManager">客户端管理器</param>
    public BatchCommandViewModel(ILogger logger, ClientManager clientManager)
    {
        _logger = logger;
        _clientManager = clientManager;

        // 创建命令
        ExecuteSelectedCommand = ReactiveCommand.CreateFromTask(ExecuteSelectedAsync);
        ExecuteAllOnlineCommand = ReactiveCommand.CreateFromTask(ExecuteAllOnlineAsync);
        ExecuteGroupCommand = ReactiveCommand.CreateFromTask<GroupModel>(ExecuteGroupAsync);
        SaveTemplateCommand = ReactiveCommand.CreateFromTask(SaveTemplateAsync);
        DeleteTemplateCommand = ReactiveCommand.CreateFromTask<CommandTemplate>(DeleteTemplateAsync);
        LoadTemplateCommand = ReactiveCommand.Create<CommandTemplate>(LoadTemplate);
        ClearCommand = ReactiveCommand.Create(Clear);

        // 初始化命令模板
        CommandTemplates = new ObservableCollection<CommandTemplate>();
        InitializeDefaultTemplates();

        // 初始化执行历史
        ExecutionHistory = new ObservableCollection<CommandExecution>();
    }

    /// <summary>
    /// 命令文本
    /// </summary>
    public string CommandText
    {
        get => _commandText;
        set => SetProperty(ref _commandText, value);
    }

    /// <summary>
    /// 选中的模板
    /// </summary>
    public CommandTemplate? SelectedTemplate
    {
        get => _selectedTemplate;
        set => SetProperty(ref _selectedTemplate, value);
    }

    /// <summary>
    /// 是否正在执行
    /// </summary>
    public bool IsExecuting
    {
        get => _isExecuting;
        set => SetProperty(ref _isExecuting, value);
    }

    /// <summary>
    /// 执行状态
    /// </summary>
    public string ExecutionStatus
    {
        get => _executionStatus;
        set => SetProperty(ref _executionStatus, value);
    }

    /// <summary>
    /// 命令模板列表
    /// </summary>
    public ObservableCollection<CommandTemplate> CommandTemplates { get; }

    /// <summary>
    /// 执行历史
    /// </summary>
    public ObservableCollection<CommandExecution> ExecutionHistory { get; }

    /// <summary>
    /// 选中客户端数量
    /// </summary>
    public int SelectedClientCount => _clientManager.GetSelectedClients().Length;

    /// <summary>
    /// 在线客户端数量
    /// </summary>
    public int OnlineClientCount => _clientManager.OnlineCount;

    /// <summary>
    /// 执行选中客户端命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> ExecuteSelectedCommand { get; }

    /// <summary>
    /// 执行所有在线客户端命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> ExecuteAllOnlineCommand { get; }

    /// <summary>
    /// 执行分组命令
    /// </summary>
    public ReactiveCommand<GroupModel, Unit> ExecuteGroupCommand { get; }

    /// <summary>
    /// 保存模板命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> SaveTemplateCommand { get; }

    /// <summary>
    /// 删除模板命令
    /// </summary>
    public ReactiveCommand<CommandTemplate, Unit> DeleteTemplateCommand { get; }

    /// <summary>
    /// 加载模板命令
    /// </summary>
    public ReactiveCommand<CommandTemplate, Unit> LoadTemplateCommand { get; }

    /// <summary>
    /// 清空命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearCommand { get; }

    /// <summary>
    /// 执行选中客户端命令
    /// </summary>
    private async Task ExecuteSelectedAsync()
    {
        if (string.IsNullOrWhiteSpace(_commandText))
        {
            ExecutionStatus = "命令不能为空";
            return;
        }

        var selectedClients = _clientManager.GetSelectedClients();
        if (selectedClients.Length == 0)
        {
            ExecutionStatus = "请先选择客户端";
            return;
        }

        await ExecuteCommandAsync(selectedClients, "选中客户端");
    }

    /// <summary>
    /// 执行所有在线客户端命令
    /// </summary>
    private async Task ExecuteAllOnlineAsync()
    {
        if (string.IsNullOrWhiteSpace(_commandText))
        {
            ExecutionStatus = "命令不能为空";
            return;
        }

        var onlineClients = _clientManager.Clients
            .Where(c => c.Status == ClientStatus.Online)
            .ToArray();

        if (onlineClients.Length == 0)
        {
            ExecutionStatus = "没有在线客户端";
            return;
        }

        await ExecuteCommandAsync(onlineClients, "所有在线客户端");
    }

    /// <summary>
    /// 执行分组命令
    /// </summary>
    private async Task ExecuteGroupAsync(GroupModel group)
    {
        if (group == null || string.IsNullOrWhiteSpace(_commandText))
        {
            ExecutionStatus = "分组或命令不能为空";
            return;
        }

        var groupClients = group.Clients
            .Where(c => c.Status == ClientStatus.Online)
            .ToArray();

        if (groupClients.Length == 0)
        {
            ExecutionStatus = $"分组 {group.Name} 中没有在线客户端";
            return;
        }

        await ExecuteCommandAsync(groupClients, $"分组 {group.Name}");
    }

    /// <summary>
    /// 执行命令
    /// </summary>
    private async Task ExecuteCommandAsync(ClientModel[] clients, string target)
    {
        try
        {
            IsExecuting = true;
            ExecutionStatus = $"正在向 {target} ({clients.Length}个客户端) 发送命令...";

            var execution = new CommandExecution
            {
                Id = Guid.NewGuid().ToString(),
                Command = _commandText,
                Target = target,
                ClientCount = clients.Length,
                StartTime = DateTime.Now,
                Status = "执行中"
            };

            ExecuteOnUIThread(() => ExecutionHistory.Insert(0, execution));

            var successCount = 0;
            var failureCount = 0;

            foreach (var client in clients)
            {
                try
                {
                    await _clientManager.SendCommandAsync(_commandText, client.ClientId);
                    successCount++;
                    
                    ExecutionStatus = $"已发送 {successCount}/{clients.Length} 个命令...";
                    
                    // 添加小延迟避免过快发送
                    await Task.Delay(50);
                }
                catch (Exception ex)
                {
                    failureCount++;
                    _logger.Warning(ex, "发送命令到客户端失败: {ClientId}", client.ClientId);
                }
            }

            execution.EndTime = DateTime.Now;
            execution.SuccessCount = successCount;
            execution.FailureCount = failureCount;
            execution.Status = failureCount == 0 ? "成功" : $"部分成功 ({successCount}/{clients.Length})";

            ExecutionStatus = $"命令执行完成: 成功 {successCount}, 失败 {failureCount}";
            
            _logger.Information("批量命令执行完成: {Command} -> {Target}, 成功: {Success}, 失败: {Failure}", 
                _commandText, target, successCount, failureCount);
        }
        catch (Exception ex)
        {
            ExecutionStatus = $"执行失败: {ex.Message}";
            _logger.Error(ex, "批量命令执行失败");
        }
        finally
        {
            IsExecuting = false;
        }
    }

    /// <summary>
    /// 保存命令模板
    /// </summary>
    private async Task SaveTemplateAsync()
    {
        if (string.IsNullOrWhiteSpace(_commandText))
        {
            ExecutionStatus = "命令不能为空";
            return;
        }

        // TODO: 实现模板名称输入对话框
        var templateName = $"模板_{DateTime.Now:MMdd_HHmm}";

        var template = new CommandTemplate
        {
            Id = Guid.NewGuid().ToString(),
            Name = templateName,
            Command = _commandText,
            Description = $"创建于 {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
            Category = "自定义",
            CreatedTime = DateTime.Now
        };

        ExecuteOnUIThread(() => CommandTemplates.Add(template));
        
        ExecutionStatus = $"模板 '{templateName}' 保存成功";
        _logger.Information("保存命令模板: {Name} -> {Command}", templateName, _commandText);
    }

    /// <summary>
    /// 删除命令模板
    /// </summary>
    private async Task DeleteTemplateAsync(CommandTemplate template)
    {
        if (template == null)
            return;

        ExecuteOnUIThread(() => CommandTemplates.Remove(template));
        
        ExecutionStatus = $"模板 '{template.Name}' 删除成功";
        _logger.Information("删除命令模板: {Name}", template.Name);
    }

    /// <summary>
    /// 加载命令模板
    /// </summary>
    private void LoadTemplate(CommandTemplate template)
    {
        if (template == null)
            return;

        CommandText = template.Command;
        SelectedTemplate = template;
        ExecutionStatus = $"已加载模板: {template.Name}";
    }

    /// <summary>
    /// 清空命令
    /// </summary>
    private void Clear()
    {
        CommandText = string.Empty;
        SelectedTemplate = null;
        ExecutionStatus = "已清空";
    }

    /// <summary>
    /// 初始化默认模板
    /// </summary>
    private void InitializeDefaultTemplates()
    {
        var defaultTemplates = new[]
        {
            new CommandTemplate { Name = "获取系统信息", Command = "systeminfo", Category = "系统", Description = "获取详细系统信息" },
            new CommandTemplate { Name = "查看进程列表", Command = "tasklist", Category = "系统", Description = "显示当前运行的进程" },
            new CommandTemplate { Name = "查看网络连接", Command = "netstat -an", Category = "网络", Description = "显示网络连接状态" },
            new CommandTemplate { Name = "重启计算机", Command = "shutdown /r /t 60", Category = "系统", Description = "60秒后重启计算机" },
            new CommandTemplate { Name = "关闭计算机", Command = "shutdown /s /t 60", Category = "系统", Description = "60秒后关闭计算机" },
            new CommandTemplate { Name = "取消关机", Command = "shutdown /a", Category = "系统", Description = "取消计划的关机操作" },
            new CommandTemplate { Name = "清理临时文件", Command = "del /q /s %temp%\\*", Category = "维护", Description = "清理临时文件夹" },
            new CommandTemplate { Name = "检查磁盘", Command = "chkdsk C: /f", Category = "维护", Description = "检查C盘错误" }
        };

        foreach (var template in defaultTemplates)
        {
            template.Id = Guid.NewGuid().ToString();
            template.CreatedTime = DateTime.Now;
            CommandTemplates.Add(template);
        }
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    public void UpdateStatistics()
    {
        OnPropertiesChanged(nameof(SelectedClientCount), nameof(OnlineClientCount));
    }
}

/// <summary>
/// 命令模板
/// </summary>
public class CommandTemplate
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Command { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public DateTime CreatedTime { get; set; }
}

/// <summary>
/// 命令执行记录
/// </summary>
public class CommandExecution
{
    public string Id { get; set; } = string.Empty;
    public string Command { get; set; } = string.Empty;
    public string Target { get; set; } = string.Empty;
    public int ClientCount { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string Status { get; set; } = string.Empty;
    
    public string Duration => EndTime.HasValue 
        ? $"{(EndTime.Value - StartTime).TotalSeconds:F1}秒" 
        : "执行中";
}
