<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:AvaloniaControlCenter.ViewModels"
             x:Class="AvaloniaControlCenter.Views.GroupManagerView"
             x:DataType="vm:GroupManagerViewModel">

    <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="6">
        <Grid RowDefinitions="Auto,*,Auto">
            
            <!-- 标题栏 -->
            <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E0E0E0" 
                    BorderThickness="0,0,0,1" Padding="12" CornerRadius="6,6,0,0">
                <Grid ColumnDefinitions="*,Auto">
                    <TextBlock Grid.Column="0" Text="分组管理" 
                               FontWeight="Bold" FontSize="14" 
                               VerticalAlignment="Center"/>
                    <Button Grid.Column="1" Content="🔄" 
                            Command="{Binding RefreshGroupsCommand}"
                            ToolTip.Tip="刷新分组列表"
                            Padding="8,4" FontSize="12"/>
                </Grid>
            </Border>

            <!-- 分组列表 -->
            <ScrollViewer Grid.Row="1" Padding="8">
                <StackPanel Spacing="4">
                    <ItemsControl ItemsSource="{Binding Groups}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="White" BorderBrush="#E9ECEF" 
                                        BorderThickness="1" CornerRadius="4" 
                                        Margin="0,2" Padding="8">
                                    <Grid ColumnDefinitions="Auto,*,Auto,Auto,Auto">
                                        
                                        <!-- 展开/折叠按钮 -->
                                        <Button Grid.Column="0" 
                                                Content="{Binding IsExpanded, Converter={StaticResource ExpandCollapseConverter}}"
                                                Background="Transparent" BorderThickness="0"
                                                Padding="4" Margin="0,0,8,0"
                                                FontSize="12" VerticalAlignment="Center"/>
                                        
                                        <!-- 分组信息 -->
                                        <StackPanel Grid.Column="1" Spacing="2">
                                            <TextBlock Text="{Binding Name}" 
                                                       FontWeight="SemiBold" FontSize="13"/>
                                            <TextBlock Text="{Binding Description}" 
                                                       FontSize="11" Foreground="Gray"/>
                                            <TextBlock FontSize="10" Foreground="#6C757D">
                                                <Run Text="在线:"/>
                                                <Run Text="{Binding OnlineCount}" Foreground="#28A745"/>
                                                <Run Text="/"/>
                                                <Run Text="{Binding TotalCount}"/>
                                            </TextBlock>
                                        </StackPanel>
                                        
                                        <!-- 分组状态 -->
                                        <Border Grid.Column="2" 
                                                Background="{Binding OnlineCount, Converter={StaticResource OnlineCountToColorConverter}}"
                                                CornerRadius="8" Padding="6,2" Margin="8,0">
                                            <TextBlock Text="{Binding OnlineCount}" 
                                                       Foreground="White" FontSize="10" 
                                                       FontWeight="Bold" HorizontalAlignment="Center"/>
                                        </Border>
                                        
                                        <!-- 操作按钮 -->
                                        <StackPanel Grid.Column="3" Orientation="Horizontal" 
                                                    Spacing="4" Margin="8,0,0,0">
                                            <Button Content="✏️" 
                                                    Command="{Binding $parent[UserControl].((vm:GroupManagerViewModel)DataContext).RenameGroupCommand}"
                                                    CommandParameter="{Binding}"
                                                    ToolTip.Tip="重命名分组"
                                                    Padding="6,4" FontSize="10"/>
                                            <Button Content="🗑️" 
                                                    Command="{Binding $parent[UserControl].((vm:GroupManagerViewModel)DataContext).DeleteGroupCommand}"
                                                    CommandParameter="{Binding}"
                                                    ToolTip.Tip="删除分组"
                                                    Padding="6,4" FontSize="10"
                                                    IsEnabled="{Binding Name, Converter={StaticResource NotDefaultGroupConverter}}"/>
                                        </StackPanel>
                                        
                                        <!-- 更多操作 -->
                                        <Button Grid.Column="4" Content="⋮" 
                                                Background="Transparent" BorderThickness="0"
                                                Padding="6,4" FontSize="12" Margin="4,0,0,0">
                                            <Button.Flyout>
                                                <MenuFlyout>
                                                    <MenuItem Header="展开所有客户端" Icon="📂"/>
                                                    <MenuItem Header="折叠所有客户端" Icon="📁"/>
                                                    <Separator/>
                                                    <MenuItem Header="导出客户端列表" Icon="📄"/>
                                                    <MenuItem Header="批量操作" Icon="⚙️"/>
                                                    <Separator/>
                                                    <MenuItem Header="分组属性" Icon="ℹ️"/>
                                                </MenuFlyout>
                                            </Button.Flyout>
                                        </Button>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </ScrollViewer>

            <!-- 创建新分组 -->
            <Border Grid.Row="2" Background="#F8F9FA" BorderBrush="#E0E0E0" 
                    BorderThickness="0,1,0,0" Padding="12" CornerRadius="0,0,6,6">
                <Grid ColumnDefinitions="*,Auto">
                    <TextBox Grid.Column="0" 
                             Text="{Binding NewGroupName}" 
                             Watermark="输入新分组名称..."
                             Margin="0,0,8,0"/>
                    <Button Grid.Column="1" 
                            Content="➕ 创建分组" 
                            Command="{Binding CreateGroupCommand}"
                            IsEnabled="{Binding !IsCreatingGroup}"
                            Classes="primary"
                            Padding="12,6"/>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
