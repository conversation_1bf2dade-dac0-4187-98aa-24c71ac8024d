<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="using:AvaloniaControlCenter.Converters"
             xmlns:local="using:AvaloniaControlCenter"
             x:Class="AvaloniaControlCenter.App"
             RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>

    <Application.Resources>
        <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
        <converters:ExpandCollapseConverter x:Key="ExpandCollapseConverter"/>
        <converters:OnlineCountToColorConverter x:Key="OnlineCountToColorConverter"/>
        <converters:NotDefaultGroupConverter x:Key="NotDefaultGroupConverter"/>
        <converters:GroupNameValidationConverter x:Key="GroupNameValidationConverter"/>
        <converters:ClientCountToTextConverter x:Key="ClientCountToTextConverter"/>
        <converters:GroupStatusToIconConverter x:Key="GroupStatusToIconConverter"/>
    </Application.Resources>

    <Application.Styles>
        <FluentTheme />
        <StyleInclude Source="/Styles/VirtualizedClientListStyle.axaml"/>
        
        <!-- 自定义样式 -->
        <Style Selector="Window">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI,Segoe UI,Arial"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <!-- 客户端列表项样式 -->
        <Style Selector="Border.client-item">
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Background" Value="Transparent"/>
        </Style>
        
        <Style Selector="Border.client-item:pointerover">
            <Setter Property="Background" Value="#F5F5F5"/>
        </Style>
        
        <Style Selector="Border.client-item.selected">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
        </Style>
        
        <!-- 状态指示器样式 -->
        <Style Selector="Ellipse.status-online">
            <Setter Property="Fill" Value="#4CAF50"/>
        </Style>
        
        <Style Selector="Ellipse.status-offline">
            <Setter Property="Fill" Value="#9E9E9E"/>
        </Style>
        
        <!-- 按钮样式 -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
        </Style>
        
        <Style Selector="Button.primary:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#1976D2"/>
        </Style>
        
        <Style Selector="Button.danger">
            <Setter Property="Background" Value="#F44336"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#F44336"/>
        </Style>
        
        <Style Selector="Button.danger:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#D32F2F"/>
        </Style>
    </Application.Styles>
</Application>
