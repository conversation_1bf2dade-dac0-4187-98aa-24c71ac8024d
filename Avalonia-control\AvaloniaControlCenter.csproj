<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    <ApplicationIcon>Assets\icon.ico</ApplicationIcon>
    <AssemblyTitle>Avalonia Control Center</AssemblyTitle>
    <AssemblyDescription>高性能远程客户端控制中心</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <AvaloniaResource Include="Assets\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.0.10" />
    <PackageReference Include="Avalonia.Desktop" Version="11.0.10" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.0.10" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.0.10" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.0.10" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.0.10" />
    <PackageReference Include="ReactiveUI" Version="19.5.41" />
    <PackageReference Include="ReactiveUI.Fody" Version="19.5.41" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.4" />
    <PackageReference Include="SkiaSharp" Version="2.88.7" />
    <PackageReference Include="Avalonia.Skia" Version="11.0.10" />
    <PackageReference Include="System.Threading.Channels" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Assets\Icons\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Controls\" />
    <Folder Include="Converters\" />
  </ItemGroup>

</Project>
