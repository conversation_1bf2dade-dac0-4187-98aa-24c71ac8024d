# server.py
import asyncio
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, Set
import json
from datetime import datetime
from redis import asyncio as aioredis
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_beijing_time():
    """获取北京时间"""
    utc_now = datetime.utcnow()
    beijing_time = utc_now + timedelta(hours=8)
    return beijing_time.strftime("%Y-%m-%d %H:%M:%S")

class ClientManager:
    def __init__(self):
        self.active_clients: Dict[str, WebSocket] = {}
        self.client_info: Dict[str, dict] = {}
        self.control_connections: Set[WebSocket] = set()
        self.redis = None
        self.HEARTBEAT_TIMEOUT = 90
        self.disconnecting_clients = set()
        # 新增: 连接状态追踪
        self.connection_states = {}
        # 新增: 最后活动时间追踪
        self.last_activity = {}
        # 新增: 连接状态检查间隔
        self.CONNECTION_CHECK_INTERVAL = 60
        # 新增: 重连尝试次数限制
        self.MAX_RECONNECT_ATTEMPTS = 3
        self.reconnect_attempts = {}
        # 添加文件管理相关的状态追踪
        self.file_manager_sessions = {}  # 存储文件管理会话信息
        # 添加最后广播状态的哈希值，避免重复广播
        self.last_broadcast_hash = None
        # 添加客户端重连防抖
        self.client_reconnect_debounce = {}
        self.RECONNECT_DEBOUNCE_TIME = 5  # 5秒内不允许同一客户端重复连接

    async def connect_redis(self):
        """连接Redis并加载客户端信息"""
        try:
            self.redis = await aioredis.from_url('redis://localhost')
            logger.info("成功连接到Redis")
            # 加载现有客户端信息 
            await self.load_clients_from_redis()
        except Exception as e:
            logger.error(f"连接Redis失败: {e}")
            # Redis连接失败不应该影响整个应用的启动
            logger.warning("Redis连接失败，将以无Redis模式运行")
            self.redis = None

    async def save_client_to_redis(self, client_id: str, info: dict):
        """保存客户端基本信息到Redis，不包含状态信息"""
        try:
            # 移除状态相关信息
            save_info = info.copy()
            save_info.pop('status', None)
            save_info.pop('last_seen', None)
            
            # 读取现有的备注信息
            existing_data = await self.redis.hget(f"client:{client_id}", "info")
            if existing_data:
                existing_info = json.loads(existing_data.decode('utf-8'))
                # 保留现有的备注信息
                if 'remark' in existing_info:
                    save_info['remark'] = existing_info['remark']
            
            await self.redis.hset(
                f"client:{client_id}",
                mapping={
                    "info": json.dumps(save_info),
                    "registration_time": save_info.get('registration_time', get_beijing_time())
                }
            )
        except Exception as e:
            logger.error(f"保存客户端信息到Redis失败: {e}")

    async def register_control(self, websocket: WebSocket):
        """注册控制端连接"""
        try:
            await websocket.accept()
            self.control_connections.add(websocket)
            logger.info("控制端连接已注册")
            
            # 发送初始状态 - 包含所有客户端（在线+离线）
            await self.send_initial_status(websocket)
            
        except Exception as e:
            logger.error(f"注册控制端失败: {e}")
            try:
                await websocket.close()
            except:
                pass
            raise

    async def disconnect_control(self, websocket: WebSocket):
        """断开控制端连接"""
        try:
            if websocket in self.control_connections:
                self.control_connections.remove(websocket)
                try:
                    await websocket.close()
                except:
                    pass
                logger.info("控制端连接已断开")
        except Exception as e:
            logger.error(f"断开控制端连接时出错: {e}")
            
    async def load_clients_from_redis(self):
        """从Redis只加载基本客户端信息"""
        if not self.redis:
            return
            
        try:
            self.client_info.clear()
            keys = await self.redis.keys("client:*")
            
            for key in keys:
                try:
                    client_id = key.decode('utf-8').split(':')[1]
                    data = await self.redis.hgetall(key)
                    
                    if b"info" in data:
                        info = json.loads(data[b"info"].decode('utf-8'))
                        # 设置默认线状态
                        info["status"] = "offline"
                        # 确保备注信息被保留
                        self.client_info[client_id] = info
                        
                except Exception as e:
                    logger.error(f"加载客户端 {client_id} 信息失败: {e}")
                    continue
                    
            logger.info(f"从Redis加载了 {len(self.client_info)} 个客户端基本信息")
            
        except Exception as e:
            logger.error(f"从Redis加载客户端信息失败: {e}")

    # 删除了过时的 broadcast_online_clients_to_control 方法
    # 现在使用 send_initial_status 和 broadcast_single_client_status 

    async def check_client_heartbeats(self):
        """定期检查客户端心跳"""
        while True:
            try:
                current_time = datetime.now()
                clients_to_mark_offline = []
                
                # 检查所有客户端的最后在线时间
                for client_id, info in self.client_info.items():
                    data = await self.redis.hget(f"client:{client_id}", "last_seen")
                    if data:
                        last_seen = datetime.fromisoformat(data.decode('utf-8'))
                        if current_time - last_seen > timedelta(seconds=self.HEARTBEAT_TIMEOUT):
                            clients_to_mark_offline.append(client_id)
                
                # 标记离线客户端并广播状态变化
                for client_id in clients_to_mark_offline:
                    if client_id in self.client_info:
                        was_online = self.client_info[client_id].get("status") == "online"
                        if was_online:
                            self.client_info[client_id]["status"] = "offline"
                            self.client_info[client_id]["last_seen"] = get_beijing_time()
                            logger.info(f"客户端 {client_id} 因心跳超时被标记为离线")
                            # 立即广播单个客户端状态变化
                            await self.broadcast_single_client_status(client_id, "offline")
                    
            except Exception as e:
                logger.error(f"检查客户端心跳时出错: {e}")
                
            await asyncio.sleep(30)  # 每30秒检查一次
            
    async def check_connection_state(self, client_id: str):
        """检查单个客户端的连接状态"""
        try:
            current_time = datetime.now()
            if client_id in self.last_activity:
                last_active = self.last_activity[client_id]
                # 使用 total_seconds() 计算总时间差
                time_diff = (current_time - last_active).total_seconds()
                
                if time_diff > self.HEARTBEAT_TIMEOUT:
                    logger.warning(f"客户端 {client_id} 超时未活动，上次活动时间: {last_active}, 超时: {time_diff}秒")
                    await self.disconnect_client(client_id)
                    return False
            
            # 检查连接状态
            if client_id in self.active_clients:
                return self.connection_states.get(client_id, False)
            
            return False
            
        except Exception as e:
            logger.error(f"检查客户端 {client_id} 连接状态时出错: {e}")
            await self.disconnect_client(client_id)
            return False

    async def cleanup_stale_connections(self):
        """定期清理过期连接"""
        while True:
            try:
                clients_to_check = list(self.active_clients.keys())
                
                for client_id in clients_to_check:
                    try:
                        # 检查连接状态
                        is_connected = await self.check_connection_state(client_id)
                        
                        if not is_connected:
                            # 检查重连次数
                            attempts = self.reconnect_attempts.get(client_id, 0)
                            if attempts < self.MAX_RECONNECT_ATTEMPTS:
                                logger.info(f"尝试重连客户端 {client_id}, 第 {attempts + 1} 次尝试")
                                self.reconnect_attempts[client_id] = attempts + 1
                            else:
                                logger.warning(f"客户端 {client_id} 达到最大重连次数，停止重连")
                                await self.disconnect_client(client_id)
                    
                    except Exception as e:
                        logger.error(f"清理客户端 {client_id} 连接时出错: {e}")
                
                # 理超时的控制端连接
                control_to_remove = set()
                for control in self.control_connections:
                    try:
                        await control.send_json({"type": "ping"})
                    except Exception:
                        control_to_remove.add(control)
                
                for control in control_to_remove:
                    await self.disconnect_control(control)
                
            except Exception as e:
                logger.error(f"清理过期连接时出错: {e}")
            
            await asyncio.sleep(self.CONNECTION_CHECK_INTERVAL)

    async def register_client(self, client_id: str, websocket: WebSocket, info: dict):
        """注册客户端连接"""
        try:
            # 检查重连防抖
            current_time = datetime.now()
            if client_id in self.client_reconnect_debounce:
                last_connect_time = self.client_reconnect_debounce[client_id]
                time_diff = (current_time - last_connect_time).total_seconds()
                if time_diff < self.RECONNECT_DEBOUNCE_TIME:
                    logger.warning(f"客户端 {client_id} 重连过于频繁，距离上次连接仅 {time_diff:.1f} 秒")
                    # 如果是频繁重连，不广播状态变化，只更新连接
                    if client_id in self.active_clients:
                        old_websocket = self.active_clients[client_id]
                        if old_websocket != websocket:
                            try:
                                asyncio.create_task(self._close_websocket_safely(old_websocket))
                                logger.info(f"关闭客户端 {client_id} 的旧连接（频繁重连）")
                            except Exception as e:
                                logger.error(f"关闭旧连接时出错: {e}")
                    
                    # 更新连接但不广播状态变化
                    self.connection_states[client_id] = True
                    self.last_activity[client_id] = datetime.now()
                    self.reconnect_attempts[client_id] = 0
                    self.active_clients[client_id] = websocket
                    
                    # 更新客户端信息但保持状态为在线
                    if client_id not in self.client_info:
                        self.client_info[client_id] = info.copy()
                        self.client_info[client_id]["status"] = "online"
                        self.client_info[client_id]["last_seen"] = get_beijing_time()
                        if "registration_time" not in self.client_info[client_id]:
                            self.client_info[client_id]["registration_time"] = get_beijing_time()
                        if "remark" not in self.client_info[client_id]:
                            self.client_info[client_id]["remark"] = ""
                    else:
                        # 更新信息但保持状态和备注
                        existing_info = self.client_info[client_id]
                        new_info = info.copy()
                        new_info["registration_time"] = existing_info.get("registration_time")
                        new_info["remark"] = existing_info.get("remark", "")
                        new_info["status"] = "online"  # 保持在线状态
                        new_info["last_seen"] = get_beijing_time()
                        self.client_info[client_id] = new_info
                    
                    if self.redis:
                        await self.save_client_to_redis(client_id, self.client_info[client_id])
                    
                    logger.info(f"客户端频繁重连注册成功（无状态广播）: {client_id}")
                    self.client_reconnect_debounce[client_id] = current_time
                    return
            
            self.client_reconnect_debounce[client_id] = current_time
            
            # 正常重连处理（非频繁重连）
            # 如果存在旧连接，直接关闭它
            if client_id in self.active_clients:
                old_websocket = self.active_clients[client_id]
                if old_websocket != websocket:  # 确保不是同一个连接
                    try:
                        # 先从活动连接中移除
                        self.active_clients.pop(client_id, None)
                        # 异步关闭旧连接，不等待
                        asyncio.create_task(self._close_websocket_safely(old_websocket))
                        logger.info(f"关闭客户端 {client_id} 的旧连接")
                    except Exception as e:
                        logger.error(f"关闭旧连接时出错: {e}")
            
            # 更新连接状态
            self.connection_states[client_id] = True
            self.last_activity[client_id] = datetime.now()
            self.reconnect_attempts[client_id] = 0
            self.active_clients[client_id] = websocket
            
            # 更新客户端信息
            existing_info = None
            if client_id in self.client_info:
                existing_info = self.client_info[client_id].copy()
            elif self.redis:
                existing_data = await self.redis.hget(f"client:{client_id}", "info")
                if existing_data:
                    existing_info = json.loads(existing_data.decode('utf-8'))

            new_info = info.copy()
            
            if existing_info:
                new_info["registration_time"] = existing_info.get("registration_time")
                if "remark" in existing_info:
                    new_info["remark"] = existing_info["remark"]

            new_info["status"] = "online"
            new_info["last_seen"] = get_beijing_time()
            
            if not existing_info:
                new_info["registration_time"] = get_beijing_time()
                new_info["remark"] = ""

            self.client_info[client_id] = new_info
            
            if self.redis:
                await self.save_client_to_redis(client_id, new_info)
            
            logger.info(f"客户端注册成功: {client_id}")
            # 只有在状态改变时才广播
            if not existing_info or existing_info.get("status") != "online":
                logger.info(f"客户端 {client_id} 首次注册或状态从离线变为在线，广播状态更新")
                await self.broadcast_single_client_status(client_id, "online")
            
        except Exception as e:
            logger.error(f"注册客户端 {client_id} 时出错: {str(e)}")
            self.connection_states[client_id] = False
            raise

    async def _close_websocket_safely(self, websocket: WebSocket):
        """安全关闭WebSocket连接"""
        try:
            if websocket:
                await websocket.close()
        except Exception as e:
            logger.debug(f"关闭WebSocket连接时出错: {e}")


    async def disconnect_client(self, client_id: str):
        """断开客户端连接"""
        if client_id in self.disconnecting_clients:
            return

        self.disconnecting_clients.add(client_id)
        try:
            self.connection_states[client_id] = False
            
            # 检查状态是否真的发生变化
            was_online = client_id in self.client_info and self.client_info[client_id].get("status") == "online"
            
            # 更新客户端信息
            if client_id in self.client_info:
                self.client_info[client_id]["status"] = "offline"
                self.client_info[client_id]["last_seen"] = get_beijing_time()

            # 关闭连接
            if client_id in self.active_clients:
                try:
                    websocket = self.active_clients.pop(client_id)
                    await websocket.close()
                except Exception:
                    pass

            # 更新Redis
            if self.redis:
                await self.save_client_to_redis(client_id, self.client_info[client_id])
            
            # 只有在状态从在线变为离线时才广播
            if was_online:
                logger.info(f"客户端 {client_id} 状态从在线变为离线，广播状态更新")
                await self.broadcast_single_client_status(client_id, "offline")
            
        except Exception as e:
            logger.error(f"断开客户端 {client_id} 连接时出错: {str(e)}")
        finally:
            self.disconnecting_clients.remove(client_id)
            
    async def update_client_heartbeat(self, client_id: str, info: dict = None):
        """更新客户端心跳"""
        try:
            if client_id in self.disconnecting_clients:
                return

            # 更新最后活动时间为当前时间
            self.last_activity[client_id] = datetime.now()
            current_time = get_beijing_time()
            
            # 检查状态是否发生变化
            was_offline = client_id not in self.client_info or self.client_info[client_id].get("status") != "online"

            if client_id in self.client_info:
                if info:  # 如果心跳包包含新的系统信息，则更新
                    existing_info = self.client_info[client_id]
                    new_info = info.copy()
                    
                    # 保护重要字段
                    new_info["registration_time"] = existing_info.get("registration_time")
                    if "remark" in existing_info:
                        new_info["remark"] = existing_info["remark"]
                    
                    # 更新状态信息
                    new_info["status"] = "online"
                    new_info["last_seen"] = current_time
                    
                    # 更新客户端信息
                    self.client_info[client_id].update(new_info)
                else:
                    # 仅更新状态信息
                    self.client_info[client_id]["status"] = "online"
                    self.client_info[client_id]["last_seen"] = current_time
                
                # 保存到Redis
                if self.redis:
                    await self.save_client_to_redis(client_id, self.client_info[client_id])
                    # 更新最后心跳时间
                    await self.redis.hset(f"client:{client_id}", "last_seen", current_time)
                
                # 重置重连计数
                self.reconnect_attempts[client_id] = 0
                
                # 更新连接状态
                self.connection_states[client_id] = True
                
                # 记录心跳日志
                logger.debug(f"收到客户端心跳: {client_id}, 最后在线时间: {current_time}")
                
                # 只有在状态从离线变为在线时才广播更新
                if was_offline:
                    logger.info(f"客户端 {client_id} 状态从离线变为在线，广播状态更新")
                    await self.broadcast_single_client_status(client_id, "online")
                
        except Exception as e:
            logger.error(f"更新客户端 {client_id} 心跳时出错: {str(e)}")
    
    async def broadcast_single_client_status(self, client_id: str, status: str):
        """广播单个客户端状态变化"""
        if not self.control_connections:
            logger.debug("没有活动的控制端连接")
            return
            
        try:
            if client_id not in self.client_info:
                logger.warning(f"客户端 {client_id} 不存在于客户端信息中")
                return
                
            client_info = self.client_info[client_id].copy()
            
            # 确保备注信息存在
            if "remark" not in client_info:
                if self.redis:
                    stored_data = await self.redis.hget(f"client:{client_id}", "info")
                    if stored_data:
                        stored_info = json.loads(stored_data.decode('utf-8'))
                        if "remark" in stored_info:
                            client_info["remark"] = stored_info["remark"]
                            
            if "remark" not in client_info:
                client_info["remark"] = client_info.get("hostname", "未知")
            
            status_message = {
                "type": "client_status_change",
                "client_id": client_id,
                "client": client_info
            }
            
            logger.info(f"广播单个客户端状态变化: {client_id} -> {status}")
            
            disconnected = set()
            for control in self.control_connections:
                try:
                    await control.send_json(status_message)
                    logger.debug(f"成功发送客户端状态变化到控制端: {client_id}")
                except Exception as e:
                    logger.error(f"广播客户端状态变化失败: {e}")
                    disconnected.add(control)
                    
            # 清理断开的控制端
            for control in disconnected:
                await self.disconnect_control(control)
                
        except Exception as e:
            logger.error(f"广播单个客户端状态失败: {e}")
            
    async def broadcast_status_to_control(self, force=False, only_online=True):
        """广播状态到所有控制端 - 仅用于特殊情况（如备注更新）"""
        if not self.control_connections:
            logger.debug("没有活动的控制端连接")
            return
            
        # 只在强制更新时使用（如备注更新），否则应该使用新的单个客户端状态变化
        if not force:
            logger.warning("broadcast_status_to_control 应该只在强制更新时使用，请使用 broadcast_single_client_status")
            return
            
        try:
            # 为每个客户端准备完整的信息
            clients_info = {}
            for client_id, info in self.client_info.items():
                # 如果只发送在线客户端，跳过离线客户端
                if only_online and info.get('status') != 'online':
                    continue
                    
                # 创建信息副本
                client_info = info.copy()
                
                # 确保备注信息存在
                if "remark" not in client_info:
                    # 如果没有备注，从 Redis 获取
                    if self.redis:
                        stored_data = await self.redis.hget(f"client:{client_id}", "info")
                        if stored_data:
                            stored_info = json.loads(stored_data.decode('utf-8'))
                            if "remark" in stored_info:
                                client_info["remark"] = stored_info["remark"]
                        
                # 如果仍然没有备注，使用主机名
                if "remark" not in client_info:
                    client_info["remark"] = client_info.get("hostname", "未知")
                    
                clients_info[client_id] = client_info

            # 计算状态哈希值，避免重复广播
            import hashlib
            status_str = json.dumps(clients_info, sort_keys=True)
            status_hash = hashlib.md5(status_str.encode()).hexdigest()
            
            if status_hash == self.last_broadcast_hash:
                logger.debug("状态未变化，跳过广播")
                return
            
            self.last_broadcast_hash = status_hash
            
            # 统计在线和离线客户端数量
            online_count = sum(1 for info in clients_info.values() if info.get('status') == 'online')
            offline_count = len(clients_info) - online_count

            # 发送兼容的旧格式消息（仅用于备注更新等特殊情况）
            status_message = {
                "type": "status_update",
                "clients": clients_info
            }
            
            logger.info(f"强制广播状态更新（兼容模式） - 总数: {len(clients_info)}, 在线: {online_count}, 离线: {offline_count}")
            
            disconnected = set()
            for control in self.control_connections:
                try:
                    await control.send_json(status_message)
                    logger.debug("成功发送状态更新到控制端")
                except Exception as e:
                    logger.error(f"广播状态到控制端失败: {e}")
                    disconnected.add(control)
                    
            # 清理断开的控制端
            for control in disconnected:
                await self.disconnect_control(control)
                
        except Exception as e:
            logger.error(f"广播状态更新失败: {e}")
            
    async def send_initial_status(self, control: WebSocket):
        """发送初始状态到控制端 - 发送所有客户端（在线+离线）"""
        try:
            # 准备所有客户端信息
            all_clients = {}
            for client_id, info in self.client_info.items():
                client_info = info.copy()
                
                # 确保备注信息存在
                if "remark" not in client_info:
                    if self.redis:
                        stored_data = await self.redis.hget(f"client:{client_id}", "info")
                        if stored_data:
                            stored_info = json.loads(stored_data.decode('utf-8'))
                            if "remark" in stored_info:
                                client_info["remark"] = stored_info["remark"]
                                
                if "remark" not in client_info:
                    client_info["remark"] = client_info.get("hostname", "未知")
                    
                all_clients[client_id] = client_info
            
            online_count = sum(1 for info in all_clients.values() if info.get('status') == 'online')
            offline_count = len(all_clients) - online_count
            
            logger.info(f"发送初始状态到控制端 - 总数: {len(all_clients)}, 在线: {online_count}, 离线: {offline_count}")
            
            await control.send_json({
                "type": "initial_status",
                "clients": all_clients
            })
        except Exception as e:
            logger.error(f"发送初始状态时出错: {e}")
            raise
            
    async def send_to_client(self, client_id: str, message: dict):
        """发送消息到指定客户端"""
        try:
            # 首先检查客户端是否在活动连接中
            if client_id not in self.active_clients:
                logger.warning(f"客户端 {client_id} 不在活动连接中，无法发送消息")
                return
            
            # 检查连接状态
            if not self.connection_states.get(client_id, False):
                logger.warning(f"客户端 {client_id} 连接状态为False，无法发送消息")
                return
            
            websocket = self.active_clients[client_id]
            
            # 检查WebSocket状态
            if websocket.client_state.name != 'CONNECTED':
                logger.warning(f"客户端 {client_id} WebSocket状态不是CONNECTED: {websocket.client_state.name}")
                await self.disconnect_client(client_id)
                return
                
            try:
                # 如果是文件管理器命令，确保包含所有必要字段
                if isinstance(message, dict) and \
                   (message.get("command", "").startswith("file_manager_") or \
                    message.get("type", "").startswith("file_manager_")):
                    file_manager_message = {
                        "command": message.get("command"),
                        "type": message.get("type"),
                        "path": message.get("path")
                    }
                    # 移除None值的键
                    message = {k: v for k, v in file_manager_message.items() if v is not None}
                
                await websocket.send_json(message)
                self.last_activity[client_id] = datetime.now()
                logger.info(f"消息已发送到客户端 {client_id}: {message}")
                
            except WebSocketDisconnect:
                logger.warning(f"客户端 {client_id} 已断开连接")
                await self.disconnect_client(client_id)
            except Exception as e:
                error_msg = str(e)
                if "accept" in error_msg.lower() or "websocket" in error_msg.lower():
                    logger.warning(f"客户端 {client_id} WebSocket连接异常: {error_msg}")
                    await self.disconnect_client(client_id)
                else:
                    logger.error(f"发送消息到客户端 {client_id} 失败: {error_msg}")
                    await self.disconnect_client(client_id)
                    
        except Exception as e:
            logger.error(f"发送消息到客户端 {client_id} 时出错: {e}")
            await self.disconnect_client(client_id)

    async def _handle_command_response(self, client_id: str, message: dict):
        """处理命令响应的辅助方法"""
        try:
            for control in self.control_connections:
                try:
                    if message.get("type") == "command":
                        await control.send_json({
                            "type": "command_sent",
                            "client_id": client_id,
                            "command": message.get("command", "")
                        })
                    else:  # new_command
                        await control.send_json({
                            "type": "new_command_sent",
                            "client_id": client_id,
                            "command": message.get("command", ""),
                            "args": message.get("args", [])
                        })
                except Exception as e:
                    logger.error(f"发送命令响应到控制端失败: {e}")
        except Exception as e:
            logger.error(f"处理命令响应时出错: {e}")
            
    async def broadcast_to_clients(self, message: dict):
        """广播消息到所有客户端"""
        disconnected = set()
        for client_id, client in self.active_clients.items():
            try:
                # 检查WebSocket状态
                if client.client_state.name == 'CONNECTED':
                    await client.send_json(message)
                else:
                    logger.warning(f"客户端 {client_id} WebSocket状态异常: {client.client_state.name}")
                    disconnected.add(client_id)
            except Exception as e:
                error_msg = str(e)
                if "accept" in error_msg.lower() or "websocket" in error_msg.lower():
                    logger.warning(f"客户端 {client_id} WebSocket连接异常: {error_msg}")
                else:
                    logger.error(f"广播消息到客户端 {client_id} 时出错: {error_msg}")
                disconnected.add(client_id)
                
        for client_id in disconnected:
            await self.disconnect_client(client_id)

    async def handle_file_manager_message(self, client_id: str, message: dict):
        """处理文件管理器相关消息"""
        try:
            msg_type = message.get('type')
            
            if msg_type == 'file_manager_init':
                # 初始化文件管理会话
                self.file_manager_sessions[client_id] = {
                    'current_path': '',
                    'history': [],
                    'history_index': -1
                }
                logger.info(f"文件管理器会话已初始化: {client_id}")
                
            elif msg_type == 'file_manager_get_drives':
                # 获取驱动器列表的请求会直接转发给客户端
                if client_id in self.active_clients:
                    await self.active_clients[client_id].send_json(message)
                    logger.info(f"已发送获取驱动器列表请求: {client_id}")
                    
            elif msg_type == 'file_manager_get_directory':
                # 获取目录内容的请求
                if client_id in self.active_clients:
                    # 更新当前路径
                    if client_id in self.file_manager_sessions:
                        self.file_manager_sessions[client_id]['current_path'] = message.get('path', '')
                    await self.active_clients[client_id].send_json(message)
                    logger.info(f"已发送获取目录内容请求: {client_id}")
                    
            elif msg_type == 'file_manager_delete':
                # 删除文件/目录的请求
                if client_id in self.active_clients:
                    await self.active_clients[client_id].send_json(message)
                    logger.info(f"已发送删除请求: {client_id}")
                    
            # 可以添加更多文件管理相关的消息处理...
            
        except Exception as e:
            logger.error(f"处理文件管理器消息时出错: {e}")
            # 向控制端发送错误消息
            error_message = {
                "type": "file_manager_error",
                "client_id": client_id,
                "error": str(e)
            }
            await self.broadcast_to_control([error_message])

    async def handle_file_manager_response(self, client_id: str, response: dict):
        """处理来自客户端的文件管理器响应"""
        try:
            # 确保响应中包含客户端ID
            response['client_id'] = client_id
            
            # 根据响应类型进行处理
            if response.get('type') in ['file_manager_drives', 'file_manager_directory_content']:
                # 这些响应需要转发给控制端
                for control in self.control_connections:
                    try:
                        await control.send_json(response)
                    except Exception as e:
                        logger.error(f"发送文件管理器响应到控制端失败: {e}")
                        
            elif response.get('type') == 'file_manager_error':
                # 处理错误响应
                logger.error(f"文件管理器错误: {response.get('error')}")
                # 转发错误消息到控制端
                await self.broadcast_to_control([response])
                
        except Exception as e:
            logger.error(f"处理文件管理器响应时出错: {e}")

manager = ClientManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    await manager.connect_redis()
    # 启动清理任务
    cleanup_task = asyncio.create_task(manager.cleanup_stale_connections())
    logger.info("服务器启动中...")
    try:
        yield
    finally:
        # 取消清理任务
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass
        if manager.redis:
            await manager.redis.close()
        logger.info("服务器关闭中...")

app = FastAPI(lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.websocket("/ws/client/{client_id}")
async def websocket_client(websocket: WebSocket, client_id: str):
    """WebSocket客户端连接处理"""
    logger.info(f"客户端尝试连接: {client_id}")
    
    try:
        # 先接受连接
        await websocket.accept()
        
        # 设置一个超时时间来接收客户端初始信息
        try:
            client_info = await asyncio.wait_for(websocket.receive_json(), timeout=10.0)
        except asyncio.TimeoutError:
            logger.error(f"客户端 {client_id} 在10秒内未发送初始信息，关闭连接")
            await websocket.close()
            return
        except Exception as e:
            logger.error(f"接收客户端 {client_id} 初始信息失败: {e}")
            await websocket.close()
            return
            
        # 注册客户端
        try:
            await manager.register_client(client_id, websocket, client_info)
        except Exception as e:
            logger.error(f"注册客户端 {client_id} 失败: {e}")
            await websocket.close()
            return
            
        # 消息处理循环
        while True:
            try:
                # 设置接收消息的超时
                data = await asyncio.wait_for(websocket.receive_json(), timeout=60.0)
                
                # 处理心跳包
                if data.get("type") == "heartbeat":
                    heartbeat_info = {k: v for k, v in data.items() if k != "type"}
                    await manager.update_client_heartbeat(client_id, heartbeat_info)
                    continue
                
                # 处理文件管理器相关的响应
                if data.get("type") in ["file_manager_drives", "file_manager_directory_content"]:
                    # 直接转发给所有控制端
                    for control in manager.control_connections:
                        try:
                            await control.send_json(data)
                            logger.info(f"已转发文件管理器响应到控制端: {data}")
                        except Exception as e:
                            logger.error(f"转发文件管理器响应失败: {e}")
                    continue
                
                # 处理其他类型的消息
                if data.get("type") == "screen_data":
                    logger.info(f"收到客户端 {client_id} 的屏幕数据，正在转发给 {len(manager.control_connections)} 个控制端")
                    disconnected_controls = set()
                    for control in manager.control_connections:
                        try:
                            if control.client_state.name == 'CONNECTED':
                                await control.send_json(data)
                                logger.debug(f"成功转发屏幕数据到控制端")
                            else:
                                logger.warning(f"控制端连接状态异常: {control.client_state.name}")
                                disconnected_controls.add(control)
                        except Exception as e:
                            logger.error(f"发送屏幕数据失败: {e}")
                            disconnected_controls.add(control)
                    
                    # 清理断开的控制端连接
                    for control in disconnected_controls:
                        await manager.disconnect_control(control)
                        
                elif data.get("type") == "command_response":
                    disconnected_controls = set()
                    for control in manager.control_connections:
                        try:
                            if control.client_state.name == 'CONNECTED':
                                await control.send_json(data)
                            else:
                                logger.warning(f"控制端连接状态异常: {control.client_state.name}")
                                disconnected_controls.add(control)
                        except Exception as e:
                            logger.error(f"发送命令结果失败: {e}")
                            disconnected_controls.add(control)
                    
                    # 清理断开的控制端连接
                    for control in disconnected_controls:
                        await manager.disconnect_control(control)
                            
            except asyncio.TimeoutError:
                # 60秒没有收到消息，发送ping检查连接
                try:
                    await websocket.ping()
                except Exception:
                    logger.warning(f"客户端 {client_id} ping失败，连接可能已断开")
                    break
                    
            except WebSocketDisconnect:
                logger.info(f"客户端正常断开连接: {client_id}")
                break
            except Exception as e:
                logger.error(f"处理客户端消息时出错 {client_id}: {str(e)}")
                # 如果是连接相关的错误，退出循环
                if "WebSocket" in str(e) or "connection" in str(e).lower() or "accept" in str(e).lower():
                    logger.warning(f"客户端 {client_id} 连接错误，退出消息处理循环: {str(e)}")
                    break
                # 其他错误继续处理
                continue
                
    except Exception as e:
        logger.error(f"客户端 {client_id} WebSocket连接处理出错: {str(e)}")
    finally:
        # 清理文件管理器会话
        if client_id in manager.file_manager_sessions:
            del manager.file_manager_sessions[client_id]
        # 处理断开连接
        if client_id in manager.active_clients:
            await manager.disconnect_client(client_id)

@app.websocket("/ws/control")
async def websocket_control(websocket: WebSocket):
    try:
        logger.info("控制端尝试连接")
        await manager.register_control(websocket)
        
        while True:
            try:
                data = await websocket.receive_json()
                logger.info(f"收到控制端消息: {data}")  # 添加日志
                # 处理备注更新
                if data.get("type") == "update_remark":
                    client_id = data.get("client_id")
                    new_remark = data.get("remark")
                    if client_id and new_remark and manager.redis:
                        try:
                            # 获取现有客户端信息
                            client_data = await manager.redis.hget(f"client:{client_id}", "info")
                            if client_data:
                                client_info = json.loads(client_data.decode('utf-8'))
                                # 更新备注
                                client_info['remark'] = new_remark
                                # 保存回 Redis
                                await manager.redis.hset(
                                    f"client:{client_id}",
                                    "info",
                                    json.dumps(client_info)
                                )
                                # 更新内存中的数据
                                if client_id in manager.client_info:
                                    manager.client_info[client_id]['remark'] = new_remark
                                    # 广播更新到所有控制端
                                    await manager.broadcast_status_to_control(force=True, only_online=True)
                            logger.info(f"已更新客户端 {client_id} 的备注为: {new_remark}")
                        except Exception as e:
                            logger.error(f"更新备注时出错: {str(e)}")

                # 处理文件管理器相关命令
                elif data.get("type", "").startswith("file_manager_") or \
                     (data.get("command", "").startswith("file_manager_")):
                    client_id = data.get("client_id")
                    if client_id in manager.active_clients:
                        try:
                            # 构造文件管理器消息
                            file_manager_message = {
                                "command": data.get("command"),
                                "type": data.get("type"),
                                "path": data.get("path")
                            }
                            # 移除None值的键
                            file_manager_message = {k: v for k, v in file_manager_message.items() if v is not None}
                            
                            await manager.active_clients[client_id].send_json(file_manager_message)
                            logger.info(f"已转发文件管理器命令到客户端 {client_id}: {file_manager_message}")
                        except Exception as e:
                            logger.error(f"发送文件管理器命令失败: {e}")
                    else:
                        logger.warning(f"客户端 {client_id} 不存在或离线")

                # 处理其他类型的消息
                elif data.get("type") == "screen_data":
                    # 直接转发屏幕数据
                    for client in manager.active_clients.values():
                        try:
                            await client.send_json(data)
                        except Exception as e:
                            logger.error(f"转发屏幕数据失败: {e}")
                elif data.get("type") == "screen_settings_update":
                    # 直接转发屏幕数据
                    for client in manager.active_clients.values():
                        try:
                            await client.send_json(data)
                        except Exception as e:
                            logger.error(f"转发屏幕数据失败: {e}")
                elif data.get("command") == "disconnect_all_screens":
                    # 处理断开所有屏幕共享命令
                    logger.info("收到断开所有屏幕共享命令，正在向所有在线客户端发送停止命令")
                    disconnect_count = 0
                    for client_id, client_ws in manager.active_clients.items():
                        try:
                            await client_ws.send_json({
                                "type": "command",
                                "command": "screen_share_stop"
                            })
                            disconnect_count += 1
                            logger.info(f"已向客户端 {client_id} 发送屏幕共享停止命令")
                        except Exception as e:
                            logger.error(f"向客户端 {client_id} 发送停止命令失败: {e}")
                    
                    # 向控制端发送确认消息
                    try:
                        await websocket.send_json({
                            "type": "disconnect_all_screens_response",
                            "success": True,
                            "message": f"已向 {disconnect_count} 个客户端发送屏幕共享停止命令"
                        })
                    except Exception as e:
                        logger.error(f"发送确认消息失败: {e}")
                elif data.get("broadcast"):
                    await manager.broadcast_to_clients(data["message"])
                    logger.info("正在广播消息到所有客户端")
                elif "client_id" in data and "message" in data:
                    client_id = data["client_id"]
                    await manager.send_to_client(client_id, data["message"])
                    logger.info(f"发送命令到客户端 {client_id}")
                elif "client_id" in data and "command" in data:
                    # 处理直接命令格式
                    client_id = data["client_id"]
                    if client_id in manager.active_clients:
                        try:
                            client_ws = manager.active_clients[client_id]
                            if client_ws.client_state.name == 'CONNECTED':
                                await client_ws.send_json(data)
                                logger.info(f"已转发命令到客户端 {client_id}: {data}")
                            else:
                                logger.warning(f"客户端 {client_id} WebSocket状态异常: {client_ws.client_state.name}")
                                await manager.disconnect_client(client_id)
                        except Exception as e:
                            error_msg = str(e)
                            if "accept" in error_msg.lower() or "websocket" in error_msg.lower():
                                logger.warning(f"客户端 {client_id} WebSocket连接异常: {error_msg}")
                                await manager.disconnect_client(client_id)
                            else:
                                logger.error(f"转发命令失败: {error_msg}")
                    else:
                        logger.warning(f"客户端 {client_id} 不存在或离线")
                else:
                    logger.warning(f"收到未知格式的消息: {data}")
                    
            except WebSocketDisconnect:
                logger.info("控制端断开连接")
                break
            except Exception as e:
                logger.error(f"处理控制端消息时出错: {str(e)}")
                continue
    finally:
        await manager.disconnect_control(websocket)
        logger.info("控制端断开连接")

@app.get("/")
async def get_status():
    """健康检查端点"""
    return {
        "status": "running", 
        "client_count": len(manager.active_clients),
        "control_count": len(manager.control_connections)
    }

@app.get("/api/ip-location")
async def get_ip_location(ip: str):
    """获取IP地理位置信息"""
    try:
        # 导入qqwry IP查询模块
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        # 尝试导入本地的qqwry模块
        try:
            from qqwry_ip import CzIp
            qqwry_path = os.path.join(os.path.dirname(__file__), "qqwry.dat")
            if not os.path.exists(qqwry_path):
                qqwry_path = os.path.join(os.path.dirname(__file__), "..", "qqwry.dat")
            
            if os.path.exists(qqwry_path):
                cz = CzIp(qqwry_path)
                location = cz.get_addr_by_ip(ip)
                # 清理位置信息
                if location and location != "未找到该IP的地址":
                    # 移除多余的空格和特殊字符
                    location = location.replace("  ", " ").strip()
                    return {"location": location}
            
        except Exception as e:
            logger.error(f"使用qqwry查询IP失败: {e}")
        
        # 如果本地查询失败，返回默认值
        return {"location": "未知位置"}
        
    except Exception as e:
        logger.error(f"IP地理位置查询失败: {e}")
        return {"location": "未知位置"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8081, log_level="info")