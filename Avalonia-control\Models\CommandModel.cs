using System;
using System.Text.Json.Serialization;

namespace AvaloniaControlCenter.Models;

/// <summary>
/// 命令类型枚举
/// </summary>
public enum CommandType
{
    /// <summary>
    /// 普通命令
    /// </summary>
    Command,
    
    /// <summary>
    /// 文件管理
    /// </summary>
    FileManager,
    
    /// <summary>
    /// 屏幕共享
    /// </summary>
    ScreenShare,
    
    /// <summary>
    /// CMD命令
    /// </summary>
    CmdCommand,
    
    /// <summary>
    /// 系统信息
    /// </summary>
    SystemInfo,
    
    /// <summary>
    /// 心跳
    /// </summary>
    Heartbeat
}

/// <summary>
/// 命令模型
/// </summary>
public class CommandModel
{
    /// <summary>
    /// 命令类型
    /// </summary>
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 命令内容
    /// </summary>
    [JsonPropertyName("command")]
    public string Command { get; set; } = string.Empty;

    /// <summary>
    /// 目标客户端ID
    /// </summary>
    [JsonPropertyName("client_id")]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 命令参数
    /// </summary>
    [JsonPropertyName("parameters")]
    public object? Parameters { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public string Timestamp { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

    /// <summary>
    /// 命令ID
    /// </summary>
    [JsonPropertyName("command_id")]
    public string CommandId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 创建普通命令
    /// </summary>
    /// <param name="command">命令内容</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>命令模型</returns>
    public static CommandModel CreateCommand(string command, string clientId = "")
    {
        return new CommandModel
        {
            Type = "command",
            Command = command,
            ClientId = clientId
        };
    }

    /// <summary>
    /// 创建文件管理命令
    /// </summary>
    /// <param name="action">操作类型</param>
    /// <param name="path">路径</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>命令模型</returns>
    public static CommandModel CreateFileManagerCommand(string action, string path, string clientId)
    {
        return new CommandModel
        {
            Type = "file_manager",
            Command = action,
            ClientId = clientId,
            Parameters = new { path }
        };
    }

    /// <summary>
    /// 创建屏幕共享命令
    /// </summary>
    /// <param name="action">操作类型（start/stop）</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>命令模型</returns>
    public static CommandModel CreateScreenShareCommand(string action, string clientId)
    {
        return new CommandModel
        {
            Type = "screen_share",
            Command = action,
            ClientId = clientId
        };
    }

    /// <summary>
    /// 创建CMD命令
    /// </summary>
    /// <param name="cmdCommand">CMD命令</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>命令模型</returns>
    public static CommandModel CreateCmdCommand(string cmdCommand, string clientId)
    {
        return new CommandModel
        {
            Type = "cmd",
            Command = $"cmd {cmdCommand}",
            ClientId = clientId
        };
    }
}
