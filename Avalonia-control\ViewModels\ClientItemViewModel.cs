using AvaloniaControlCenter.Models;
using ReactiveUI;
using System;
using System.Reactive;

namespace AvaloniaControlCenter.ViewModels;

/// <summary>
/// 客户端项ViewModel
/// </summary>
public class ClientItemViewModel : ViewModelBase
{
    private ClientModel _client;
    private bool _isSelected;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="client">客户端模型</param>
    public ClientItemViewModel(ClientModel client)
    {
        _client = client;
        
        // 创建命令
        SelectCommand = ReactiveCommand.Create(OnSelect);
        OpenScreenShareCommand = ReactiveCommand.Create(OnOpenScreenShare);
        OpenFileManagerCommand = ReactiveCommand.Create(OnOpenFileManager);
        OpenCmdCommand = ReactiveCommand.Create(OnOpenCmd);
    }

    /// <summary>
    /// 客户端模型
    /// </summary>
    public ClientModel Client
    {
        get => _client;
        set => SetProperty(ref _client, value);
    }

    /// <summary>
    /// 是否被选中
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            if (SetProperty(ref _isSelected, value))
            {
                _client.IsSelected = value;
            }
        }
    }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId => _client.ClientId;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName => _client.DisplayName;

    /// <summary>
    /// 主机名
    /// </summary>
    public string Hostname => _client.Hostname;

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark => _client.Remark;

    /// <summary>
    /// CPU信息
    /// </summary>
    public string CpuInfo => _client.CpuInfo;

    /// <summary>
    /// 内存信息
    /// </summary>
    public string Memory => _client.Memory;

    /// <summary>
    /// 显卡信息
    /// </summary>
    public string GpuInfo => _client.GpuInfo;

    /// <summary>
    /// 操作系统信息
    /// </summary>
    public string OsInfo => _client.OsInfo;

    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress => _client.IpAddress;

    /// <summary>
    /// IP归属地
    /// </summary>
    public string IpLocation => _client.IpLocation;

    /// <summary>
    /// 状态
    /// </summary>
    public ClientStatus Status => _client.Status;

    /// <summary>
    /// 状态文本
    /// </summary>
    public string StatusText => _client.StatusText;

    /// <summary>
    /// 最后在线时间显示
    /// </summary>
    public string LastSeenDisplay => _client.LastSeenDisplay;

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline => _client.Status == ClientStatus.Online;

    /// <summary>
    /// 状态颜色
    /// </summary>
    public string StatusColor => _client.Status switch
    {
        ClientStatus.Online => "#4CAF50",
        ClientStatus.Connecting => "#FF9800",
        ClientStatus.Disconnecting => "#FF5722",
        _ => "#9E9E9E"
    };

    /// <summary>
    /// 选择命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> SelectCommand { get; }

    /// <summary>
    /// 打开屏幕共享命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> OpenScreenShareCommand { get; }

    /// <summary>
    /// 打开文件管理命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> OpenFileManagerCommand { get; }

    /// <summary>
    /// 打开CMD命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> OpenCmdCommand { get; }

    /// <summary>
    /// 选择事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? Selected;

    /// <summary>
    /// 屏幕共享事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? ScreenShareRequested;

    /// <summary>
    /// 文件管理事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? FileManagerRequested;

    /// <summary>
    /// CMD事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? CmdRequested;

    /// <summary>
    /// 更新客户端信息
    /// </summary>
    /// <param name="client">新的客户端信息</param>
    public void UpdateClient(ClientModel client)
    {
        _client = client;
        
        // 通知所有属性变更
        OnPropertiesChanged(
            nameof(DisplayName),
            nameof(Hostname),
            nameof(Remark),
            nameof(CpuInfo),
            nameof(Memory),
            nameof(GpuInfo),
            nameof(OsInfo),
            nameof(IpAddress),
            nameof(IpLocation),
            nameof(Status),
            nameof(StatusText),
            nameof(LastSeenDisplay),
            nameof(IsOnline),
            nameof(StatusColor)
        );
    }

    private void OnSelect()
    {
        IsSelected = !IsSelected;
        Selected?.Invoke(this, this);
    }

    private void OnOpenScreenShare()
    {
        if (IsOnline)
        {
            ScreenShareRequested?.Invoke(this, this);
        }
    }

    private void OnOpenFileManager()
    {
        if (IsOnline)
        {
            FileManagerRequested?.Invoke(this, this);
        }
    }

    private void OnOpenCmd()
    {
        if (IsOnline)
        {
            CmdRequested?.Invoke(this, this);
        }
    }
}
