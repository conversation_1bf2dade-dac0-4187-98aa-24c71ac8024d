# upload_module.py

import asyncio
import httpx
import logging
from pathlib import Path
from typing import List, Dict, Callable
import aiofiles
from asyncio import Semaphore
import time
import uuid
import os
from tqdm import tqdm

class AsyncUploader:
    def __init__(self, file_path: str, remote_path: str, upload_url: str, chunk_size: int = 1024*1024, 
                 max_concurrent: int = 5, progress_callback: Callable = None):
        self.file_path = Path(file_path)
        self.remote_path = remote_path  # 新增: 保存远程路径
        self.upload_url = upload_url
        self.chunk_size = chunk_size
        self.semaphore = Semaphore(max_concurrent)
        self.client = httpx.AsyncClient(timeout=30.0)
        self.uploaded_bytes = 0
        self.total_bytes = 0
        self.start_time = None
        self.progress_callback = progress_callback
        self.file_id = str(uuid.uuid4())
        self.progress_bar = None
        self.logger = logging.getLogger(__name__)

    def calc_divisional_range(self, filesize: int) -> List[tuple]:
        chunks = []
        position = 0
        while position < filesize:
            chunk_end = min(position + self.chunk_size - 1, filesize - 1)
            chunks.append((position, chunk_end))
            position = chunk_end + 1
        return chunks

    async def read_chunk(self, s_pos: int, e_pos: int) -> bytes:
        chunk_size = e_pos - s_pos + 1
        async with aiofiles.open(self.file_path, 'rb') as f:
            await f.seek(s_pos)
            return await f.read(chunk_size)

    async def upload_chunk(self, chunk: bytes, chunk_number: int, total_chunks: int) -> None:
        async with self.semaphore:
            try:
                self.logger.debug(f"Uploading chunk {chunk_number}/{total_chunks}")
                
                # 构建上传数据
                files = {
                    'file': (self.file_path.name, chunk, 'application/octet-stream')
                }
                
                data = {
                    'content_range': f'{chunk_number}/{total_chunks}',
                    'remote_path': self.remote_path  # 添加远程路径
                }
                
                headers = {
                    'file-id': self.file_id
                }
                
                self.logger.debug(f"Upload request data: {data}")
                self.logger.debug(f"Upload headers: {headers}")
                
                response = await self.client.post(
                    self.upload_url,
                    files=files,
                    data=data,
                    headers=headers
                )
                
                # 如果响应不成功，记录详细信息
                if response.status_code != 200:
                    self.logger.error(f"Upload failed with status {response.status_code}")
                    self.logger.error(f"Response content: {response.text}")
                    response.raise_for_status()
                
                # 更新进度
                chunk_size = len(chunk)
                self.uploaded_bytes += chunk_size
                
                if self.progress_bar:
                    self.progress_bar.update(chunk_size)
                    
                if self.progress_callback:
                    progress = (self.uploaded_bytes / self.total_bytes) * 100
                    self.progress_callback(progress)
                
                self.logger.debug(f"Successfully uploaded chunk {chunk_number}")
                
            except Exception as e:
                self.logger.error(f"Upload failed for chunk {chunk_number}: {str(e)}")
                raise

    def format_time(self, seconds: float) -> str:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        if hours > 0:
            return f"{int(hours)}小时{int(minutes)}分{int(seconds)}秒"
        elif minutes > 0:
            return f"{int(minutes)}分{int(seconds)}秒"
        else:
            return f"{int(seconds)}秒"

    def format_size(self, size_bytes: float) -> str:
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024.0

    async def upload_file(self):
        try:
            self.start_time = time.time()
            self.total_bytes = self.file_path.stat().st_size
            self.uploaded_bytes = 0
            
            self.logger.info(f"开始上传: {self.file_path}")
            self.logger.info(f"远程路径: {self.remote_path}")
            self.logger.info(f"文件大小: {self.format_size(self.total_bytes)}")
            
            # 如果没有提供进度回调，使用tqdm
            if not self.progress_callback:
                self.progress_bar = tqdm(
                    total=self.total_bytes,
                    unit='B',
                    unit_scale=True,
                    desc=f"上传进度"
                )
            
            ranges = self.calc_divisional_range(self.total_bytes)
            #self.logger.info(f"分片数量: {len(ranges)}")
            
            tasks = []
            for i, (s_pos, e_pos) in enumerate(ranges):
                chunk = await self.read_chunk(s_pos, e_pos)
                task = asyncio.create_task(
                    self.upload_chunk(chunk, i, len(ranges))
                )
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            total_time = time.time() - self.start_time
            average_speed = self.total_bytes / total_time
            
            if self.progress_bar:
                self.progress_bar.close()
                
            self.logger.info(f"\n上传完成: {self.file_path}")
            self.logger.info(f"总大小: {self.format_size(self.total_bytes)}")
            self.logger.info(f"总耗时: {self.format_time(total_time)}")
            self.logger.info(f"平均速度: {self.format_size(average_speed)}/s")
            
        except Exception as e:
            self.logger.error(f"上传失败: {str(e)}")
            raise
        finally:
            await self.client.aclose()
            if self.progress_bar:
                self.progress_bar.close()

class DirectoryUploader:
    def __init__(self, base_dir: str, remote_base_dir: str, upload_url: str, progress_callback: Callable = None):
        self.base_dir = Path(base_dir)
        self.remote_base_dir = remote_base_dir
        self.upload_url = upload_url
        self.progress_callback = progress_callback
        self.logger = logging.getLogger(__name__)
    
    def get_remote_path(self, local_path: Path) -> str:
        """根据本地路径生成远程路径，确保包含盘符"""
        # 获取完整路径的字符串表示
        full_path = str(local_path)
        
        # 获取盘符和路径部分
        drive, path = os.path.splitdrive(full_path)
        
        # 移除盘符中的冒号并确保是小写
        if drive:
            drive = drive.replace(':', '').lower()
        else:
            drive = 'c'  # 如果没有盘符，默认使用 C 盘
        
        # 获取相对路径部分
        rel_path = os.path.relpath(path, str(self.base_dir))
        
        # 构建远程路径（保持目录结构）
        return f"{self.remote_base_dir}/{rel_path}".replace('\\', '/')
    
    async def upload_directory(self):
        """上传整个目录"""
        try:
            files = []
            for root, _, filenames in os.walk(self.base_dir):
                for filename in filenames:
                    local_path = Path(root) / filename
                    remote_path = self.get_remote_path(local_path)
                    files.append((local_path, remote_path))
            
            total_files = len(files)
            completed_files = 0
            
            for local_path, remote_path in files:
                self.logger.info(f"Uploading {local_path} to {remote_path}")
                try:
                    uploader = AsyncUploader(
                        str(local_path),
                        remote_path,
                        self.upload_url,
                        progress_callback=self.progress_callback
                    )
                    await uploader.upload_file()
                except Exception as e:
                    self.logger.error(f"Failed to upload {local_path}: {str(e)}")
                    raise
                
                completed_files += 1
                if self.progress_callback:
                    total_progress = (completed_files / total_files) * 100
                    self.progress_callback(total_progress)
                    
        except Exception as e:
            self.logger.error(f"目录上传失败: {str(e)}")
            raise

async def main():
    uploader = AsyncUploader(
        file_path="update.zip",  # 替换为你要上传的文件
        upload_url="http://*************:8000/upload",
        chunk_size=1024*1024,  # 1MB 的分片大小
        max_concurrent=5
    )
    await uploader.upload_file()

if __name__ == "__main__":
    asyncio.run(main())