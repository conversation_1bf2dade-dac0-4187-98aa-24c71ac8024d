{"format": 1, "restore": {"F:\\web\\WebRTC远程客户管理后台\\Avalonia+c#\\Avalonia-control\\AvaloniaControlCenter.csproj": {}}, "projects": {"F:\\web\\WebRTC远程客户管理后台\\Avalonia+c#\\Avalonia-control\\AvaloniaControlCenter.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\web\\WebRTC远程客户管理后台\\Avalonia+c#\\Avalonia-control\\AvaloniaControlCenter.csproj", "projectName": "AvaloniaControlCenter", "projectPath": "F:\\web\\WebRTC远程客户管理后台\\Avalonia+c#\\Avalonia-control\\AvaloniaControlCenter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\web\\WebRTC远程客户管理后台\\Avalonia+c#\\Avalonia-control\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.ReactiveUI": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Skia": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.0.10, )"}, "ReactiveUI": {"target": "Package", "version": "[19.5.41, )"}, "ReactiveUI.Fody": {"target": "Package", "version": "[19.5.41, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.7, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.4, )"}, "System.Threading.Channels": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}