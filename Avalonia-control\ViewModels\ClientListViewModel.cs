using AvaloniaControlCenter.Models;
using AvaloniaControlCenter.Services;
using ReactiveUI;
using Serilog;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;

namespace AvaloniaControlCenter.ViewModels;

/// <summary>
/// 客户端列表ViewModel
/// </summary>
public class ClientListViewModel : ViewModelBase
{
    private readonly ILogger _logger;
    private readonly ClientManager _clientManager;
    private string _searchText = string.Empty;
    private string _selectedGroupName = "全部";
    private bool _showOnlineOnly = false;
    private ClientItemViewModel? _selectedClient;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="clientManager">客户端管理器</param>
    public ClientListViewModel(ILogger logger, ClientManager clientManager)
    {
        _logger = logger;
        _clientManager = clientManager;

        // 创建命令
        RefreshCommand = ReactiveCommand.CreateFromTask(RefreshAsync);
        ClearSelectionCommand = ReactiveCommand.Create(ClearSelection);
        SelectAllCommand = ReactiveCommand.Create(SelectAll);
        SendCommandCommand = ReactiveCommand.CreateFromTask<string>(SendCommandAsync);
        BroadcastCommandCommand = ReactiveCommand.CreateFromTask<string>(BroadcastCommandAsync);

        // 订阅客户端管理器事件
        _clientManager.ClientAdded += OnClientAdded;
        _clientManager.ClientRemoved += OnClientRemoved;
        _clientManager.ClientStatusChanged += OnClientStatusChanged;
        _clientManager.StatisticsChanged += OnStatisticsChanged;

        // 初始化过滤后的客户端列表
        FilteredClients = new ObservableCollection<ClientItemViewModel>();
        
        // 初始化分组列表
        GroupNames = new ObservableCollection<string> { "全部" };

        // 应用初始过滤
        ApplyFilter();
    }

    /// <summary>
    /// 过滤后的客户端列表
    /// </summary>
    public ObservableCollection<ClientItemViewModel> FilteredClients { get; }

    /// <summary>
    /// 分组名称列表
    /// </summary>
    public ObservableCollection<string> GroupNames { get; }

    /// <summary>
    /// 搜索文本
    /// </summary>
    public string SearchText
    {
        get => _searchText;
        set
        {
            if (SetProperty(ref _searchText, value))
            {
                ApplyFilter();
            }
        }
    }

    /// <summary>
    /// 选中的分组名称
    /// </summary>
    public string SelectedGroupName
    {
        get => _selectedGroupName;
        set
        {
            if (SetProperty(ref _selectedGroupName, value))
            {
                ApplyFilter();
            }
        }
    }

    /// <summary>
    /// 是否只显示在线客户端
    /// </summary>
    public bool ShowOnlineOnly
    {
        get => _showOnlineOnly;
        set
        {
            if (SetProperty(ref _showOnlineOnly, value))
            {
                ApplyFilter();
            }
        }
    }

    /// <summary>
    /// 选中的客户端
    /// </summary>
    public ClientItemViewModel? SelectedClient
    {
        get => _selectedClient;
        set => SetProperty(ref _selectedClient, value);
    }

    /// <summary>
    /// 在线客户端数量
    /// </summary>
    public int OnlineCount => _clientManager.OnlineCount;

    /// <summary>
    /// 总客户端数量
    /// </summary>
    public int TotalCount => _clientManager.TotalCount;

    /// <summary>
    /// 选中客户端数量
    /// </summary>
    public int SelectedCount => FilteredClients.Count(c => c.IsSelected);

    /// <summary>
    /// 统计信息文本
    /// </summary>
    public string StatisticsText => $"在线: {OnlineCount} | 总计: {TotalCount} | 选中: {SelectedCount}";

    /// <summary>
    /// 刷新命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }

    /// <summary>
    /// 清除选择命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearSelectionCommand { get; }

    /// <summary>
    /// 全选命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> SelectAllCommand { get; }

    /// <summary>
    /// 发送命令到选中客户端
    /// </summary>
    public ReactiveCommand<string, Unit> SendCommandCommand { get; }

    /// <summary>
    /// 广播命令到所有客户端
    /// </summary>
    public ReactiveCommand<string, Unit> BroadcastCommandCommand { get; }

    /// <summary>
    /// 客户端选择变更事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? ClientSelected;

    /// <summary>
    /// 屏幕共享请求事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? ScreenShareRequested;

    /// <summary>
    /// 文件管理请求事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? FileManagerRequested;

    /// <summary>
    /// CMD请求事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? CmdRequested;

    /// <summary>
    /// 应用过滤器
    /// </summary>
    private void ApplyFilter()
    {
        ExecuteOnUIThread(() =>
        {
            FilteredClients.Clear();

            var clients = _clientManager.Clients.AsEnumerable();

            // 按分组过滤
            if (_selectedGroupName != "全部")
            {
                clients = clients.Where(c => c.GroupName == _selectedGroupName);
            }

            // 按在线状态过滤
            if (_showOnlineOnly)
            {
                clients = clients.Where(c => c.Status == ClientStatus.Online);
            }

            // 按搜索文本过滤
            if (!string.IsNullOrWhiteSpace(_searchText))
            {
                var searchLower = _searchText.ToLower();
                clients = clients.Where(c =>
                    c.DisplayName.ToLower().Contains(searchLower) ||
                    c.Hostname.ToLower().Contains(searchLower) ||
                    c.IpAddress.Contains(searchLower) ||
                    c.ClientId.ToLower().Contains(searchLower));
            }

            // 按状态排序（在线优先）
            clients = clients.OrderByDescending(c => c.Status == ClientStatus.Online)
                           .ThenBy(c => c.DisplayName);

            foreach (var client in clients)
            {
                var viewModel = new ClientItemViewModel(client);
                
                // 订阅事件
                viewModel.Selected += OnClientItemSelected;
                viewModel.ScreenShareRequested += OnScreenShareRequested;
                viewModel.FileManagerRequested += OnFileManagerRequested;
                viewModel.CmdRequested += OnCmdRequested;

                FilteredClients.Add(viewModel);
            }

            // 更新统计信息
            OnPropertyChanged(nameof(SelectedCount));
            OnPropertyChanged(nameof(StatisticsText));
        });
    }

    /// <summary>
    /// 刷新客户端列表
    /// </summary>
    private Task RefreshAsync()
    {
        try
        {
            _logger.Information("刷新客户端列表");
            ApplyFilter();
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "刷新客户端列表时发生错误");
            return Task.FromException(ex);
        }
    }

    /// <summary>
    /// 清除选择
    /// </summary>
    private void ClearSelection()
    {
        foreach (var client in FilteredClients)
        {
            client.IsSelected = false;
        }
        OnPropertyChanged(nameof(SelectedCount));
        OnPropertyChanged(nameof(StatisticsText));
    }

    /// <summary>
    /// 全选
    /// </summary>
    private void SelectAll()
    {
        foreach (var client in FilteredClients)
        {
            client.IsSelected = true;
        }
        OnPropertyChanged(nameof(SelectedCount));
        OnPropertyChanged(nameof(StatisticsText));
    }

    /// <summary>
    /// 发送命令到选中客户端
    /// </summary>
    private async Task SendCommandAsync(string command)
    {
        try
        {
            await _clientManager.SendCommandToSelectedAsync(command);
            _logger.Information("发送命令到选中客户端: {Command}", command);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "发送命令到选中客户端时发生错误: {Command}", command);
        }
    }

    /// <summary>
    /// 广播命令到所有客户端
    /// </summary>
    private async Task BroadcastCommandAsync(string command)
    {
        try
        {
            await _clientManager.BroadcastCommandAsync(command);
            _logger.Information("广播命令到所有客户端: {Command}", command);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "广播命令时发生错误: {Command}", command);
        }
    }

    /// <summary>
    /// 客户端添加事件处理
    /// </summary>
    private void OnClientAdded(object? sender, ClientModel client)
    {
        // 更新分组列表
        if (!GroupNames.Contains(client.GroupName))
        {
            ExecuteOnUIThread(() => GroupNames.Add(client.GroupName));
        }

        ApplyFilter();
    }

    /// <summary>
    /// 客户端移除事件处理
    /// </summary>
    private void OnClientRemoved(object? sender, ClientModel client)
    {
        ApplyFilter();
    }

    /// <summary>
    /// 客户端状态变更事件处理
    /// </summary>
    private void OnClientStatusChanged(object? sender, ClientModel client)
    {
        // 查找对应的ViewModel并更新
        var viewModel = FilteredClients.FirstOrDefault(vm => vm.ClientId == client.ClientId);
        if (viewModel != null)
        {
            ExecuteOnUIThread(() => viewModel.UpdateClient(client));
        }
    }

    /// <summary>
    /// 统计信息变更事件处理
    /// </summary>
    private void OnStatisticsChanged(object? sender, EventArgs e)
    {
        OnPropertiesChanged(nameof(OnlineCount), nameof(TotalCount), nameof(StatisticsText));
    }

    /// <summary>
    /// 客户端项选择事件处理
    /// </summary>
    private void OnClientItemSelected(object? sender, ClientItemViewModel viewModel)
    {
        SelectedClient = viewModel;
        ClientSelected?.Invoke(this, viewModel);
        OnPropertyChanged(nameof(SelectedCount));
        OnPropertyChanged(nameof(StatisticsText));
    }

    /// <summary>
    /// 屏幕共享请求事件处理
    /// </summary>
    private void OnScreenShareRequested(object? sender, ClientItemViewModel viewModel)
    {
        ScreenShareRequested?.Invoke(this, viewModel);
    }

    /// <summary>
    /// 文件管理请求事件处理
    /// </summary>
    private void OnFileManagerRequested(object? sender, ClientItemViewModel viewModel)
    {
        FileManagerRequested?.Invoke(this, viewModel);
    }

    /// <summary>
    /// CMD请求事件处理
    /// </summary>
    private void OnCmdRequested(object? sender, ClientItemViewModel viewModel)
    {
        CmdRequested?.Invoke(this, viewModel);
    }
}
