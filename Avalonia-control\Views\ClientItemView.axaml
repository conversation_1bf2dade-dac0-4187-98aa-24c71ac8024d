<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:AvaloniaControlCenter.ViewModels"
             x:Class="AvaloniaControlCenter.Views.ClientItemView"
             x:DataType="vm:ClientItemViewModel">

    <Border Classes="client-item-virtualized" Classes.selected="{Binding IsSelected}">
        <Grid ColumnDefinitions="Auto,*,Auto" RowDefinitions="Auto,Auto,Auto">
            
            <!-- 左侧：状态指示器和选择框 -->
            <StackPanel Grid.Column="0" Grid.RowSpan="3" 
                        Orientation="Vertical" VerticalAlignment="Center" 
                        Spacing="8" Margin="0,0,16,0">
                
                <!-- 状态指示器 -->
                <Ellipse Classes="status-indicator"
                         Classes.status-online="{Binding IsOnline}"
                         Classes.status-offline="{Binding !IsOnline}"
                         Fill="{Binding StatusColor}"/>
                
                <!-- 选择框 -->
                <CheckBox Classes="client-selector"
                          IsChecked="{Binding IsSelected}" 
                          Command="{Binding SelectCommand}"/>
            </StackPanel>
            
            <!-- 中间：客户端详细信息 -->
            <StackPanel Grid.Column="1" Grid.Row="0" Grid.RowSpan="3" Spacing="6">
                
                <!-- 第一行：主要信息 -->
                <Grid ColumnDefinitions="*,Auto,Auto">
                    <!-- 客户端名称 -->
                    <TextBlock Grid.Column="0" 
                               Text="{Binding DisplayName}" 
                               Classes="client-name"
                               VerticalAlignment="Center"/>
                    
                    <!-- 状态标签 -->
                    <Border Grid.Column="1" 
                            Classes="status-badge"
                            Classes.online="{Binding IsOnline}"
                            Classes.offline="{Binding !IsOnline}">
                        <TextBlock Text="{Binding StatusText}" 
                                   Foreground="White" 
                                   FontSize="10" 
                                   FontWeight="Bold"/>
                    </Border>
                    
                    <!-- 最后在线时间 -->
                    <TextBlock Grid.Column="2" 
                               Text="{Binding LastSeenDisplay}" 
                               Classes="client-info"
                               VerticalAlignment="Center" 
                               Margin="8,0,0,0"/>
                </Grid>
                
                <!-- 第二行：网络信息 -->
                <Grid ColumnDefinitions="Auto,*,Auto,*">
                    <TextBlock Grid.Column="0" Text="IP:" Classes="client-label"/>
                    <TextBlock Grid.Column="1" Text="{Binding IpAddress}" 
                               Classes="client-ip" Margin="0,0,16,0"/>
                    <TextBlock Grid.Column="2" Text="ID:" Classes="client-label"/>
                    <TextBlock Grid.Column="3" Text="{Binding ClientId}" 
                               Classes="client-id"/>
                </Grid>
                
                <!-- 第三行：系统信息 -->
                <Grid ColumnDefinitions="Auto,*">
                    <TextBlock Grid.Column="0" Text="系统:" Classes="client-label"/>
                    <TextBlock Grid.Column="1" Text="{Binding OsInfo}" 
                               Classes="client-value"/>
                </Grid>
                
                <!-- 第四行：硬件信息 -->
                <Grid ColumnDefinitions="Auto,2*,Auto,*">
                    <TextBlock Grid.Column="0" Text="CPU:" Classes="client-label"/>
                    <TextBlock Grid.Column="1" Text="{Binding CpuInfo}" 
                               Classes="client-value" Margin="0,0,16,0"/>
                    <TextBlock Grid.Column="2" Text="内存:" Classes="client-label"/>
                    <TextBlock Grid.Column="3" Text="{Binding Memory}" 
                               Classes="client-value"/>
                </Grid>
                
                <!-- 第五行：显卡信息 -->
                <Grid ColumnDefinitions="Auto,*" IsVisible="{Binding GpuInfo, Converter={x:Static StringConverters.IsNotNullOrEmpty}}">
                    <TextBlock Grid.Column="0" Text="显卡:" Classes="client-label"/>
                    <TextBlock Grid.Column="1" Text="{Binding GpuInfo}" 
                               Classes="client-value"/>
                </Grid>
            </StackPanel>
            
            <!-- 右侧：操作按钮 -->
            <StackPanel Grid.Column="2" Grid.RowSpan="3" 
                        Orientation="Vertical" VerticalAlignment="Center" 
                        Spacing="6" Margin="16,0,0,0">
                
                <!-- 屏幕共享按钮 -->
                <Button Content="🖥️ 屏幕" 
                        Classes="client-action primary"
                        Command="{Binding OpenScreenShareCommand}"
                        IsEnabled="{Binding IsOnline}"
                        ToolTip.Tip="查看远程桌面"/>
                
                <!-- 文件管理按钮 -->
                <Button Content="📁 文件" 
                        Classes="client-action secondary"
                        Command="{Binding OpenFileManagerCommand}"
                        IsEnabled="{Binding IsOnline}"
                        ToolTip.Tip="文件管理"/>
                
                <!-- CMD命令按钮 -->
                <Button Content="⌨️ CMD" 
                        Classes="client-action success"
                        Command="{Binding OpenCmdCommand}"
                        IsEnabled="{Binding IsOnline}"
                        ToolTip.Tip="命令行终端"/>
                
                <!-- 更多操作按钮 -->
                <Button Content="⚙️ 更多" 
                        Classes="client-action"
                        IsEnabled="{Binding IsOnline}"
                        ToolTip.Tip="更多操作">
                    <Button.Flyout>
                        <MenuFlyout>
                            <MenuItem Header="重启客户端" 
                                      Icon="🔄"
                                      IsEnabled="{Binding IsOnline}"/>
                            <MenuItem Header="关闭客户端" 
                                      Icon="❌"
                                      IsEnabled="{Binding IsOnline}"/>
                            <Separator/>
                            <MenuItem Header="编辑备注" 
                                      Icon="✏️"/>
                            <MenuItem Header="移动到分组" 
                                      Icon="📂"/>
                            <Separator/>
                            <MenuItem Header="复制客户端ID" 
                                      Icon="📋"/>
                            <MenuItem Header="复制IP地址" 
                                      Icon="🌐"/>
                        </MenuFlyout>
                    </Button.Flyout>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
