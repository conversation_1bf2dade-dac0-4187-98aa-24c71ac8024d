import asyncio
import json
import logging
import websockets
import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import threading
import base64
import queue
from concurrent.futures import ThreadPoolExecutor
import os
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ScreenViewerWindow:
    def __init__(self, master, client_id: str, hostname: str = "未知主机", ip_address: str = "未知IP", control_gui = None):
        """初始化屏幕查看器窗口"""
        self.control_gui = control_gui
        self.window = tk.Toplevel(master)
        self.window.title(f"屏幕监控 - {client_id}")
        self.window.iconbitmap("icon.ico")
        # 界面位置置于主窗口中央
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() - self.window.winfo_reqwidth()) / 2
        y = (self.window.winfo_screenheight() - self.window.winfo_reqheight()) / 2
        self.window.geometry(f"+{int(x)}+{int(y)}")
        
        # 基本属性
        self.client_id = client_id
        self.running = True
        self.last_update_time = 0
        self.update_interval = 1.0 / 30  # 默认30fps
        self.close_callback = None  # 添加关闭回调属性
        
        # 初始化帧缓冲区
        self.frame = None
        self.last_frame = None
        self.frame_buffer = None
        self.frame_count = 0
        self.last_time = 0
        
        # 初始化处理队列
        self.process_queue = asyncio.Queue()
        self.display_queue = queue.Queue()
        
        # 初始化线程池
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 设置选项
        self.resolution_options = {
            "480p": (854, 480),
            "720p": (1280, 720),
            "1080p": (1920, 1080)
        }
        self.fps_options = [5, 15, 30]
        self.quality_options = {
            "高": {"compression": 90, "block_size": 256},
            "中": {"compression": 75, "block_size": 384},
            "低": {"compression": 60, "block_size": 512}
        }
        
        # 初始化默认设置
        self.current_fps = 15
        self.active_settings = {
            'resolutions': ["720p"],
            'fps_values': [15],
            'quality': "中"
        }
        
        # 显示相关
        self.fullscreen = False
        self.real_width = None
        self.real_height = None
        
        # 初始化GUIx
        self.window.after(1000 // self.current_fps, self.update_display_from_queue)
        self.setup_gui()
        
        # 启动显示更新循环

    def decompress_frame(self, data):
        """解压缩帧数据"""
        try:
            # 解码base64数据
            compressed_data = base64.b64decode(data)
            
            # 转换为numpy数组
            np_arr = np.frombuffer(compressed_data, np.uint8)
            
            # 解码图像
            frame = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
            
            if frame is None:
                raise Exception("解码图像失败")
                
            return frame
            
        except Exception as e:
            logger.error(f"解压缩帧数据失败: {e}")
            return None

    def update_frame(self, block_data):
        """更新帧中的指定区域"""
        try:
            block = self.decompress_frame(block_data['data'])
            x, y = block_data['x'], block_data['y']
            h, w = block.shape[:2]
            
            # 如果帧缓冲区还没有初始化，先初始化
            if self.frame is None:
                # 获取当前选择的分辨率
                current_res = self.resolution_var.get()
                target_width, target_height = self.resolution_options[current_res]
                self.initialize_frame(target_width, target_height)
                logger.info(f"初始化帧缓冲区: {target_width}x{target_height}")

            # 确保坐标在有效范围内
            if x < 0 or y < 0:
                return

            frame_h, frame_w = self.frame.shape[:2]
            if x >= frame_w or y >= frame_h:
                return

            # 确保块的大小不会超出帧的边界
            h = min(h, frame_h - y)
            w = min(w, frame_w - x)
            
            # 更新当前帧和备份帧
            try:
                # 直接更新当前帧
                self.frame[y:y+h, x:x+w] = block[:h, :w]
                
                # 保存完整的帧为备份
                if self.last_frame is None:
                    self.last_frame = self.frame.copy()
                else:
                    self.last_frame[y:y+h, x:x+w] = block[:h, :w]
                
            except Exception as e:
                logger.error(f"更新帧时发生数组维度错误: {e}")
                logger.debug(f"目标区域: y:{y}~{y+h}, x:{x}~{x+w}, "
                           f"块大小: {block.shape}, 帧大小: {self.frame.shape}")

        except Exception as e:
            logger.error(f"更新帧发生错误: {e}")
            logger.exception(e)
            if self.last_frame is not None:
                try:
                    self.frame = self.last_frame.copy()
                except:
                    pass

    def setup_gui(self):
        """设置GUI界面"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.window)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建工具栏
        self.toolbar = ttk.Frame(self.main_frame)
        self.toolbar.pack(fill=tk.X, padx=5, pady=5)

        # 连接状态
        self.status_label = ttk.Label(self.toolbar, text="已连接")
        self.status_label.pack(side=tk.LEFT, padx=5)

        # 设置控件
        self.setup_settings_ui()

        # 创建画布框架
        self.canvas_frame = ttk.Frame(self.main_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 屏幕显示区域 - 设置固定大小
        current_res = self.resolution_var.get()
        width, height = self.resolution_options[current_res]
        self.canvas = tk.Canvas(self.canvas_frame, 
                              width=width, 
                              height=height,
                              bg='black',
                              highlightthickness=0)
        self.canvas.pack(expand=True)

        # 状态栏
        self.status_bar = ttk.Label(self.window, text="就绪", relief=tk.SUNKEN)
        self.status_bar.pack(fill=tk.X)

        # 件绑定
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.window.bind('<Escape>', lambda e: self.exit_fullscreen())
        self.window.bind('<F11>', lambda e: self.toggle_fullscreen())

    def setup_settings_ui(self):
        """设置UI中的设置控件"""
        # 创建一个框架来容纳所有设置
        settings_frame = ttk.Frame(self.toolbar)
        settings_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 分辨率选择框
        resolution_frame = ttk.LabelFrame(settings_frame, text="分辨率")
        resolution_frame.pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        self.resolution_var = tk.StringVar(value="720p")
        for res in self.resolution_options.keys():
            ttk.Radiobutton(
                resolution_frame,
                text=res,
                value=res,
                variable=self.resolution_var,
                command=self.on_resolution_change
            ).pack(side=tk.LEFT, padx=2)
        
        # 帧率选择框
        fps_frame = ttk.LabelFrame(settings_frame, text="帧率")
        fps_frame.pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        self.fps_var = tk.IntVar(value=15)
        for fps in self.fps_options:
            ttk.Radiobutton(
                fps_frame,
                text=f"{fps}",
                value=fps,
                variable=self.fps_var,
                command=self.on_fps_change
            ).pack(side=tk.LEFT, padx=2)
            
        # 质量选择框
        quality_frame = ttk.LabelFrame(settings_frame, text="质量")
        quality_frame.pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        self.quality_var = tk.StringVar(value="中")
        for quality in self.quality_options.keys():
            ttk.Radiobutton(
                quality_frame,
                text=quality,
                value=quality,
                variable=self.quality_var,
                command=self.on_quality_change
            ).pack(side=tk.LEFT, padx=2)

        # 状态显示标签
        status_frame = ttk.Frame(settings_frame)
        status_frame.pack(side=tk.LEFT, padx=5, fill=tk.Y)

        self.fps_label = ttk.Label(status_frame, text="FPS: 0")
        self.fps_label.pack(side=tk.LEFT, padx=5)

        self.resolution_label = ttk.Label(status_frame, text="分辨率: --")
        self.resolution_label.pack(side=tk.LEFT, padx=5)

        self.quality_label = ttk.Label(status_frame, text="质量: 中")
        self.quality_label.pack(side=tk.LEFT, padx=5)

    def on_resolution_change(self):
        """处理分辨率变化"""
        selected_resolution = self.resolution_var.get()
        
        # 更新设置并发送到服务器
        self.active_settings['resolutions'] = [selected_resolution]
        self.send_settings_update({'resolutions': [selected_resolution]})
        logger.info(f"分辨率设置已更新: {selected_resolution}")
        
        # 获取新的目标分辨率
        width, height = self.resolution_options[selected_resolution]
        
        # 调整画布大小
        self.canvas.config(width=width, height=height)
        logger.info(f"画布大小已调整为: {width}x{height}")
        
        # 强制重新初始化帧缓冲区
        self.frame = None
        self.last_frame = None
        self.frame_buffer = None
        
        # 清空处理队列和显示队列
        while not self.process_queue.empty():
            try:
                self.process_queue.get_nowait()
                self.process_queue.task_done()
            except:
                pass
        
        while not self.display_queue.empty():
            try:
                self.display_queue.get_nowait()
                self.display_queue.task_done()
            except:
                pass
        
        # 调整窗口大小以适应新的画布大小
        self.window.update_idletasks()
        
        # 获取工具栏和状态栏的高度
        toolbar_height = self.toolbar.winfo_height()
        statusbar_height = self.status_bar.winfo_height()
        
        # 计算新的窗口大小
        window_width = width + 20
        window_height = height + toolbar_height + statusbar_height + 20
        
        # 获取屏幕尺寸
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # 计算窗口位置，使其居中
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        # 设置窗口大小和位置
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        logger.info(f"窗口大小已调整为: {window_width}x{window_height}")
        
        # 强制更新显示
        self.update_display()

    def on_fps_change(self):
        """处理帧率变化"""
        selected_fps = self.fps_var.get()
            
        # 更新设置并发送到服务器
        self.active_settings['fps_values'] = [selected_fps]
        self.current_fps = selected_fps
        self.send_settings_update({'fps_values': [selected_fps]})
        logger.info(f"帧率设置已更新: {selected_fps}, 显示刷新率: {self.current_fps}")

    def on_quality_change(self):
        """处理质量变化"""
        quality = self.quality_var.get()
        self.active_settings['quality'] = quality
        self.quality_label.config(text=f"质量: {quality}")
        self.send_settings_update({'quality': quality})
        logger.info(f"质量设置已更新: {quality}")

    def toggle_fullscreen(self):
        """切换全屏模式"""
        self.fullscreen = not self.fullscreen
        self.window.attributes('-fullscreen', self.fullscreen)

    def exit_fullscreen(self):
        """退出全屏模式"""
        self.fullscreen = False
        self.window.attributes('-fullscreen', False)

    def update_display_from_queue(self):
        """从显示队列更新显示"""
        try:
            # 非阻塞方式获取数据
            while not self.display_queue.empty():
                data = self.display_queue.get_nowait()
                self.update_frame(data)
                self.display_queue.task_done()
            # 更新显示
            self.update_display()
            
        except queue.Empty:
            pass
        except Exception as e:
            logger.error(f"更新显示时发生错误: {e}")
        
        # 继续定时更新
        if self.running:
            self.window.after(1000 // self.current_fps, self.update_display_from_queue)

    def update_display(self):
        """更新显示画面"""
        try:
            if self.frame is None:
                # 如果没有帧，填充黑色背景
                canvas_width = self.canvas.winfo_width()
                canvas_height = self.canvas.winfo_height()
                if canvas_width > 1 and canvas_height > 1:
                    self.canvas.delete("all")
                    self.canvas.create_rectangle(0, 0, canvas_width, canvas_height, 
                                              fill='black', outline='black')
                return

            # 获取当前选择的分辨率
            current_res = self.resolution_var.get()
            target_width, target_height = self.resolution_options[current_res]

            # 调整画布大小以匹配目标分辨率
            if self.canvas.winfo_width() != target_width or self.canvas.winfo_height() != target_height:
                self.canvas.config(width=target_width, height=target_height)
                logger.info(f"调整画布大小为: {target_width}x{target_height}")

            try:
                # 将帧转换为图像
                image = Image.fromarray(cv2.cvtColor(self.frame, cv2.COLOR_BGR2RGB))
                photo = ImageTk.PhotoImage(image)

                # 更新画布
                self.canvas.delete("all")
                self.canvas.create_image(0, 0, image=photo, anchor='nw')
                self.canvas.photo = photo  # 保持引用以防止垃圾回收

                # 更新分辨率显示
                frame_h, frame_w = self.frame.shape[:2]
                self.resolution_label.config(text=f"分辨率: {frame_w}x{frame_h}")
                
                # 更新帧计数
                self.frame_count += 1
                
            except Exception as e:
                logger.error(f"更新显示时发生错误: {e}")
                logger.exception(e)
                return

        except Exception as e:
            logger.error(f"更新显示时发生错误: {e}")
            logger.exception(e)

    async def process_screen_data(self, message):
        """处理屏幕数据"""
        try:
            # 解析组合消息
            data = json.loads(message)
            if 'header' not in data or 'data' not in data:
                logger.error("消息格式错误：缺少header或data字段")
                return

            header = data['header']
            binary_data = base64.b64decode(data['data'])
            logger.debug(f"收到屏幕数据: x={header.get('x', 0)}, y={header.get('y', 0)}")
            
            # 解码图像数据
            img_array = np.frombuffer(binary_data, dtype=np.uint8)
            frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            if frame is None:
                logger.error("无法解码图像数据")
                return

            logger.debug(f"解码成功，帧大小: {frame.shape}")

            # 将处理后的数据放入显示队列
            self.display_queue.put({
                'block': frame,
                'x': header.get('x', 0),
                'y': header.get('y', 0)
            })
            logger.debug(f"已将帧数据放入显示队列: x={header.get('x', 0)}, y={header.get('y', 0)}, size={frame.shape}")

        except Exception as e:
            logger.error(f"处理屏幕数据时发生错误: {e}")
            logger.exception(e)

    def send_settings_update(self, settings):
        """发送设置更新到服务器"""
        try:
            message = {
                'type': 'screen_settings_update',
                'command': 'screen_settings_update',
                'client_id': self.client_id,
                'settings': settings
            }
            #self.websocket.send(json.dumps(message))
            self.send_message(json.dumps(message))
            logger.info(f"已发送设置更新: {settings}")
        except Exception as e:
            logger.error(f"发送设置更新失败: {e}")

    def send_message(self, message):
        """发送消息到服务器"""
        try:
            if hasattr(self, 'control_gui') and hasattr(self.control_gui, 'websocket'):
                asyncio.run_coroutine_threadsafe(
                    self.control_gui.websocket.send(message),
                    self.control_gui.loop
                )
            else:
                logger.warning("无法发送消息：未连接到服务器")
        except Exception as e:
            logger.error(f"发送消息失败: {e}")

    def set_close_callback(self, callback):
        """设置窗口关闭时的回调函数"""
        self.close_callback = callback

    def on_closing(self):
        """窗口关闭时的处理"""
        self.running = False
        if self.close_callback:
            self.close_callback()
        self.window.destroy()

    def initialize_frame(self, width, height):
        """初始化帧缓冲区"""
        try:
            self.frame = np.zeros((height, width, 3), dtype=np.uint8)
            self.last_frame = np.zeros((height, width, 3), dtype=np.uint8)
            self.real_width = width
            self.real_height = height
            logger.info(f"帧缓冲区已初始化: {width}x{height}")
        except Exception as e:
            logger.error(f"初始化帧缓冲区时发生错误: {e}")
            logger.exception(e)

    def update_fps(self):
        """更新FPS显示"""
        current_time = cv2.getTickCount() / cv2.getTickFrequency()
        if self.last_time == 0:
            self.last_time = current_time
            return

        self.frame_count += 1
        if current_time - self.last_time >= 1.0:
            fps = self.frame_count / (current_time - self.last_time)
            self.fps_label.config(text=f"FPS: {fps:.1f}")
            self.frame_count = 0
            self.last_time = current_time

class ScreenShareManager:
    def __init__(self):
        self.screen_viewers = {}
        self.max_viewers = 8  # 限制最大监控窗口数
        self.screen_data_buffers = {}  # 为每个客户端添加独立的数据缓冲区

    async def start_screen_share(self, client_id, client_info, websocket, add_log_message):
        """启动屏幕共享"""
        if len(self.screen_viewers) >= self.max_viewers:
            add_log_message("已达到最大监控窗口数限制")
            return
            
        if client_id in self.screen_viewers:
            if self.screen_viewers[client_id].running:
                self.screen_viewers[client_id].window.lift()
                return
            else:
                del self.screen_viewers[client_id]
                if client_id in self.screen_data_buffers:
                    del self.screen_data_buffers[client_id]
        
        hostname = client_info.get('hostname', '未知主机')
        ip_address = client_info.get('ip_address', '未知IP')
        viewer = ScreenViewerWindow(f"屏幕监控 - {hostname} - {ip_address}")
        
        # 设置关闭回调
        async def stop_share():
            if websocket:
                message = {
                    "broadcast": False,
                    "client_id": client_id,
                    "message": {"command": "screen_share_stop"}
                }
                await websocket.send(json.dumps(message))
            if client_id in self.screen_viewers:
                del self.screen_viewers[client_id]
            if client_id in self.screen_data_buffers:
                del self.screen_data_buffers[client_id]
        
        viewer.set_close_callback(lambda: asyncio.run(stop_share()))
        
        self.screen_viewers[client_id] = viewer
        self.screen_data_buffers[client_id] = []  # 初始化数据缓冲区
        
        # 发送开始共享命令
        if websocket:
            message = {
                "broadcast": False,
                "client_id": client_id,
                "message": {"command": "screen_share_start"}
            }
            await websocket.send(json.dumps(message))
            add_log_message(f"开始屏幕监控: {hostname}")

    def handle_screen_data(self, client_id, data):
        """处理屏幕共享数据"""
        if client_id not in self.screen_viewers or not self.screen_viewers[client_id].running:
            return None
            
        viewer = self.screen_viewers[client_id]
        if not viewer.running:
            return None
            
        try:
            # 确保数据缓冲区存在
            if client_id not in self.screen_data_buffers:
                self.screen_data_buffers[client_id] = []

            # 确保更新的数据仅发送给指定客户端
            if client_id in self.screen_viewers:
                self.screen_data_buffers[client_id].append(data)
                viewer.update_frame(data)
                
            return True
            
        except Exception as e:
            logger.error(f"处理屏幕数据错误: {e}")
            return None

    def close_all_viewers(self):
        """关闭所有查看器"""
        for viewer in self.screen_viewers.values():
            if viewer.running:
                viewer.on_closing()
        self.screen_viewers.clear()
        self.screen_data_buffers.clear()