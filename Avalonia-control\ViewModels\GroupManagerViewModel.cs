using AvaloniaControlCenter.Models;
using AvaloniaControlCenter.Services;
using ReactiveUI;
using Serilog;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;

namespace AvaloniaControlCenter.ViewModels;

/// <summary>
/// 分组管理ViewModel
/// </summary>
public class GroupManagerViewModel : ViewModelBase
{
    private readonly ILogger _logger;
    private readonly ClientManager _clientManager;
    private string _newGroupName = string.Empty;
    private GroupModel? _selectedGroup;
    private bool _isCreatingGroup = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="clientManager">客户端管理器</param>
    public GroupManagerViewModel(ILogger logger, ClientManager clientManager)
    {
        _logger = logger;
        _clientManager = clientManager;

        // 创建命令
        CreateGroupCommand = ReactiveCommand.CreateFromTask(CreateGroupAsync);
        DeleteGroupCommand = ReactiveCommand.CreateFromTask<GroupModel>(DeleteGroupAsync);
        RenameGroupCommand = ReactiveCommand.CreateFromTask<GroupModel>(RenameGroupAsync);
        MoveClientToGroupCommand = ReactiveCommand.CreateFromTask<(ClientModel, GroupModel)>(MoveClientToGroupAsync);
        RefreshGroupsCommand = ReactiveCommand.Create(RefreshGroups);

        // 初始化分组列表
        Groups = new ObservableCollection<GroupModel>();
        RefreshGroups();
    }

    /// <summary>
    /// 分组列表
    /// </summary>
    public ObservableCollection<GroupModel> Groups { get; }

    /// <summary>
    /// 新分组名称
    /// </summary>
    public string NewGroupName
    {
        get => _newGroupName;
        set => SetProperty(ref _newGroupName, value);
    }

    /// <summary>
    /// 选中的分组
    /// </summary>
    public GroupModel? SelectedGroup
    {
        get => _selectedGroup;
        set => SetProperty(ref _selectedGroup, value);
    }

    /// <summary>
    /// 是否正在创建分组
    /// </summary>
    public bool IsCreatingGroup
    {
        get => _isCreatingGroup;
        set => SetProperty(ref _isCreatingGroup, value);
    }

    /// <summary>
    /// 创建分组命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> CreateGroupCommand { get; }

    /// <summary>
    /// 删除分组命令
    /// </summary>
    public ReactiveCommand<GroupModel, Unit> DeleteGroupCommand { get; }

    /// <summary>
    /// 重命名分组命令
    /// </summary>
    public ReactiveCommand<GroupModel, Unit> RenameGroupCommand { get; }

    /// <summary>
    /// 移动客户端到分组命令
    /// </summary>
    public ReactiveCommand<(ClientModel, GroupModel), Unit> MoveClientToGroupCommand { get; }

    /// <summary>
    /// 刷新分组命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> RefreshGroupsCommand { get; }

    /// <summary>
    /// 分组变更事件
    /// </summary>
    public event EventHandler<GroupModel>? GroupCreated;
    public event EventHandler<GroupModel>? GroupDeleted;
    public event EventHandler<GroupModel>? GroupRenamed;
    public event EventHandler<(ClientModel, GroupModel, GroupModel)>? ClientMoved;

    /// <summary>
    /// 创建新分组
    /// </summary>
    private async Task CreateGroupAsync()
    {
        if (string.IsNullOrWhiteSpace(_newGroupName))
        {
            _logger.Warning("分组名称不能为空");
            return;
        }

        if (Groups.Any(g => g.Name.Equals(_newGroupName, StringComparison.OrdinalIgnoreCase)))
        {
            _logger.Warning("分组名称已存在: {GroupName}", _newGroupName);
            return;
        }

        try
        {
            IsCreatingGroup = true;

            var newGroup = new GroupModel
            {
                Name = _newGroupName.Trim(),
                Description = $"创建于 {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                IsExpanded = true
            };

            await ExecuteOnUIThreadAsync(() =>
            {
                Groups.Add(newGroup);
                _clientManager.Groups.Add(newGroup);
            });

            _logger.Information("创建分组成功: {GroupName}", newGroup.Name);
            GroupCreated?.Invoke(this, newGroup);

            // 清空输入
            NewGroupName = string.Empty;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "创建分组失败: {GroupName}", _newGroupName);
        }
        finally
        {
            IsCreatingGroup = false;
        }
    }

    /// <summary>
    /// 删除分组
    /// </summary>
    private async Task DeleteGroupAsync(GroupModel group)
    {
        if (group == null)
            return;

        if (group.Name == "默认分组")
        {
            _logger.Warning("不能删除默认分组");
            return;
        }

        try
        {
            // 将分组中的客户端移动到默认分组
            var defaultGroup = Groups.FirstOrDefault(g => g.Name == "默认分组");
            if (defaultGroup != null && group.Clients.Count > 0)
            {
                var clientsToMove = group.Clients.ToList();
                foreach (var client in clientsToMove)
                {
                    await MoveClientToGroupAsync((client, defaultGroup));
                }
            }

            await ExecuteOnUIThreadAsync(() =>
            {
                Groups.Remove(group);
                _clientManager.Groups.Remove(group);
            });

            _logger.Information("删除分组成功: {GroupName}", group.Name);
            GroupDeleted?.Invoke(this, group);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "删除分组失败: {GroupName}", group.Name);
        }
    }

    /// <summary>
    /// 重命名分组
    /// </summary>
    private async Task RenameGroupAsync(GroupModel group)
    {
        if (group == null)
            return;

        // TODO: 实现重命名对话框
        // 这里暂时使用简单的方式
        var newName = $"{group.Name}_重命名";
        
        if (Groups.Any(g => g.Name.Equals(newName, StringComparison.OrdinalIgnoreCase)))
        {
            _logger.Warning("分组名称已存在: {GroupName}", newName);
            return;
        }

        try
        {
            var oldName = group.Name;
            group.Name = newName;

            // 更新客户端的分组名称
            foreach (var client in group.Clients)
            {
                client.GroupName = newName;
            }

            _logger.Information("重命名分组成功: {OldName} -> {NewName}", oldName, newName);
            GroupRenamed?.Invoke(this, group);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "重命名分组失败: {GroupName}", group.Name);
        }
    }

    /// <summary>
    /// 移动客户端到分组
    /// </summary>
    private async Task MoveClientToGroupAsync((ClientModel client, GroupModel targetGroup) args)
    {
        var (client, targetGroup) = args;
        
        if (client == null || targetGroup == null)
            return;

        try
        {
            // 从原分组中移除
            var sourceGroup = Groups.FirstOrDefault(g => g.Clients.Contains(client));
            if (sourceGroup != null && sourceGroup != targetGroup)
            {
                await ExecuteOnUIThreadAsync(() =>
                {
                    sourceGroup.RemoveClient(client);
                });
            }

            // 添加到目标分组
            await ExecuteOnUIThreadAsync(() =>
            {
                targetGroup.AddClient(client);
                client.GroupName = targetGroup.Name;
            });

            _logger.Information("移动客户端成功: {ClientId} -> {GroupName}", client.ClientId, targetGroup.Name);
            
            if (sourceGroup != null)
            {
                ClientMoved?.Invoke(this, (client, sourceGroup, targetGroup));
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "移动客户端失败: {ClientId} -> {GroupName}", client.ClientId, targetGroup.Name);
        }
    }

    /// <summary>
    /// 刷新分组列表
    /// </summary>
    private void RefreshGroups()
    {
        try
        {
            ExecuteOnUIThread(() =>
            {
                Groups.Clear();
                foreach (var group in _clientManager.Groups)
                {
                    Groups.Add(group);
                }
            });

            _logger.Debug("刷新分组列表完成，共 {Count} 个分组", Groups.Count);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "刷新分组列表失败");
        }
    }

    /// <summary>
    /// 获取分组统计信息
    /// </summary>
    /// <param name="group">分组</param>
    /// <returns>统计信息</returns>
    public string GetGroupStatistics(GroupModel group)
    {
        if (group == null)
            return string.Empty;

        var onlineCount = group.Clients.Count(c => c.Status == ClientStatus.Online);
        var totalCount = group.Clients.Count;
        
        return $"{group.Name} ({onlineCount}/{totalCount})";
    }

    /// <summary>
    /// 检查分组名称是否有效
    /// </summary>
    /// <param name="groupName">分组名称</param>
    /// <returns>是否有效</returns>
    public bool IsValidGroupName(string groupName)
    {
        if (string.IsNullOrWhiteSpace(groupName))
            return false;

        if (groupName.Length > 50)
            return false;

        if (Groups.Any(g => g.Name.Equals(groupName.Trim(), StringComparison.OrdinalIgnoreCase)))
            return false;

        return true;
    }

    /// <summary>
    /// 获取可用的分组列表（用于移动客户端）
    /// </summary>
    /// <param name="excludeGroup">要排除的分组</param>
    /// <returns>可用分组列表</returns>
    public GroupModel[] GetAvailableGroups(GroupModel? excludeGroup = null)
    {
        return Groups.Where(g => g != excludeGroup).ToArray();
    }
}
