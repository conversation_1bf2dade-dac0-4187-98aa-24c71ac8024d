# Avalonia + C# 控制端开发文档

## 📌 项目目标

构建一个高性能控制端程序，支持：

- ✅ 管理海量客户端（>10,000）
- ✅ 客户端频繁上线/下线/心跳（每秒数百变动）
- ✅ 实时查看远程桌面画面
- ✅ 快速响应指令操作（秒级）
- ✅ UI不卡顿，画面不卡顿，状态更新稳定

---

## ✅ 技术选型

- 桌面框架：**Avalonia UI**
- 编程语言：**C#**
- 图形引擎：**Skia（GPU 加速）**
- 网络通信：`System.Net.WebSockets`
- 架构模式：MVVM

---

## 🧠 开发前必须注意的 7 大关键点

### 1️⃣ UI 虚拟化处理

- 使用 `VirtualizingStackPanel` 或 `ItemsRepeater`
- 避免渲染全部客户端项 → 否则 UI 会瘫痪

```xml
<ItemsControl Items="{Binding ClientList}">
  <ItemsControl.ItemsPanel>
    <VirtualizingStackPanel />
  </ItemsControl.ItemsPanel>
</ItemsControl>
```

---

### 2️⃣ 客户端状态更新需异步批量处理

- 所有客户端变动通过队列暂存，再由 `DispatcherTimer` 每 100ms 刷新一次 UI
- 避免每个变动都 `PropertyChanged`，防止卡顿

---

### 3️⃣ 后台 WebSocket 与 UI 解耦

- `ClientManager` 管理状态和连接池
- 不可在 WebSocket 回调中直接更新 UI → 必须用 `Dispatcher.UIThread.Post()`

---

### 4️⃣ 远程图像使用 GPU 加速渲染

- 客户端图像通过 JPEG/PNG 解码 → `Bitmap` → 绑定到 `Image.Source`
- 建议仅对当前选中客户端启动画面接收

```csharp
ImageControl.Source = new Bitmap(new MemoryStream(jpegBytes));
```

---

### 5️⃣ 所有 UI 操作必须封装在 Dispatcher 中

- 用于跨线程安全更新 UI 状态

```csharp
Dispatcher.UIThread.Post(() => {
    client.Status = "Online";
});
```

---

### 6️⃣ 图像展示区必须分页/懒加载

- 支持显示 4\~16 个客户端画面时不卡顿
- 非活跃客户端画面可关闭订阅或降低帧率

---

### 7️⃣ 日志与容错必须完善

- 使用 Serilog/NLog 等写日志到文件/UI
- 所有连接异常、指令失败、图像接收错误都应记录

---

## 📦 推荐项目结构

```
ControlCenter/
├── ViewModels/
│   └── ClientListViewModel.cs
├── Views/
│   └── MainWindow.axaml
├── Models/
│   └── ClientModel.cs
├── Services/
│   └── WebSocketManager.cs
│   └── DispatcherQueue.cs
├── Assets/
│   └── Icons, Placeholder
└── App.axaml / Program.cs
```

---

## ⚙️ 开发调试建议

- 使用 Visual Studio / Rider / VS Code 开发
- `dotnet run` 可快速运行调试
- 启用 XAML Previewer 查看布局
- 使用模拟器生成虚拟客户端测试高并发行为

---

## 🧪 性能测试建议

| 测试项      | 测试目标                   |
| -------- | ---------------------- |
| 客户端同时在线数 | 支持 >10,000 条目不崩溃       |
| 心跳刷新频率   | 每秒 100\~500 个变更 UI 不卡顿 |
| 多画面同步    | 同时查看 4\~16 个桌面画面保持流畅   |
| 指令响应延迟   | 下发指令后客户端在 500ms 内响应    |

---

## ✅ 推荐使用工具库

- `Serilog` / `NLog` → 日志记录
- `MessagePack` / `System.Text.Json` → 协议解析
- `ReactiveUI` → 响应式 MVVM 框架
- `SkiaSharp` → 低层图像处理（必要时）

---

## 🔚 结语
严格遵循以上注意事项，即便面对海量客户端、持续状态刷新、多画面并发，也能稳定流畅运行。

