import tkinter as tk
from tkinter import ttk, messagebox
import json
import logging
from datetime import datetime
import os
from typing import Optional, Dict, List
import asyncio

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('FileManager-Controller')

class FileManagerWindow:
    def __init__(self, master, client_id: str, websocket, hostname: str = "未知主机"):
        """初始化文件管理器窗口"""
        self.window = tk.Toplevel(master)
        self.window.title(f"文件管理器 - {hostname}")
        self.window.geometry("1280x800")
        self.window.iconbitmap("icon.ico")

        # 界面位置置于主窗口中央
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() - self.window.winfo_reqwidth()) / 2
        y = (self.window.winfo_screenheight() - self.window.winfo_reqheight()) / 2
        self.window.geometry(f"+{int(x)}+{int(y)}")



        
        # 保存参数
        self.client_id = client_id
        self.websocket = websocket
        self.current_path = ""
        
        # 状态变量
        self.initialized = False
        self.drives = []
        
        # 添加导航历史
        self.history = []
        self.current_history_index = -1
        
        # 创建右键菜单
        self.context_menu = self.create_context_menu()
        
        # 创建界面
        self.setup_gui()
        
        # 初始化文件管理器
        self.start_file_manager()
        
        # 显示初始化状态
        self.status_var.set("正在初始化文件管理器...")

    def setup_gui(self):
        """设置GUI界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏
        self.create_toolbar(main_frame)
        
        # 创建导航栏
        self.create_navigation_bar(main_frame)
        
        # 创建分割窗口
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建导航树
        self.create_nav_tree(paned)
        
        # 创建文件列表
        self.create_file_list(paned)
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, padx=2, pady=2)
        
        # 添加工具栏按钮
        self.back_btn = ttk.Button(toolbar, text="返回", command=self.go_back)
        self.back_btn.pack(side=tk.LEFT, padx=2)
        
        self.forward_btn = ttk.Button(toolbar, text="前进", command=self.go_forward)
        self.forward_btn.pack(side=tk.LEFT, padx=2)
        
        ttk.Button(toolbar, text="上级", command=self.go_up).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="刷新", command=self.refresh).pack(side=tk.LEFT, padx=2)
        
        # 添加分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 添加文件操作按钮
        ttk.Button(toolbar, text="上传", command=self.upload_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="下载", command=self.download_file).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="删除", command=self.delete_item).pack(side=tk.LEFT, padx=2)
        
        # 初始化按钮状态
        self.back_btn.configure(state='disabled')
        self.forward_btn.configure(state='disabled')

    def create_navigation_bar(self, parent):
        """创建导航栏"""
        nav_frame = ttk.Frame(parent)
        nav_frame.pack(fill=tk.X, padx=2, pady=2)
        
        # 添加导航按钮
        ttk.Button(nav_frame, text="←", width=3, command=self.go_back).pack(side=tk.LEFT, padx=2)
        ttk.Button(nav_frame, text="→", width=3, command=self.go_forward).pack(side=tk.LEFT, padx=2)
        ttk.Button(nav_frame, text="↑", width=3, command=self.go_up).pack(side=tk.LEFT, padx=2)
        
        # 添加路径输入框
        self.path_var = tk.StringVar()
        self.path_entry = ttk.Entry(nav_frame, textvariable=self.path_var)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
        self.path_entry.bind('<Return>', self.on_path_enter)

    def create_nav_tree(self, parent):
        """创建导航树"""
        frame = ttk.Frame(parent)
        parent.add(frame, weight=1)
        
        # 创建导航树
        self.nav_tree = ttk.Treeview(frame, show='tree')
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Treeview",
            font=('Microsoft YaHei UI', 9),
            rowheight=24,
            background="#ffffff",
            foreground="#000000",
            fieldbackground="#ffffff"
        )
        
        # 配置标签样式
        self.nav_tree.tag_configure('drive', font=('Microsoft YaHei UI', 9))
        self.nav_tree.tag_configure('folder', font=('Microsoft YaHei UI', 9))
        
        # 设置列宽和显示
        self.nav_tree.column('#0', width=250, stretch=True)
        
        # 添加滚动条
        vsb = ttk.Scrollbar(frame, orient="vertical", command=self.nav_tree.yview)
        self.nav_tree.configure(yscrollcommand=vsb.set)
        
        # 使用grid布局
        self.nav_tree.grid(row=0, column=0, sticky='nsew')
        vsb.grid(row=0, column=1, sticky='ns')
        
        # 配置grid权重
        frame.grid_rowconfigure(0, weight=1)
        frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.nav_tree.bind('<Double-1>', self.on_nav_tree_double_click)
        self.nav_tree.bind('<<TreeviewOpen>>', self.on_tree_open)  # 添加展开事件处理
        
        logger.info("导航树创建完成")
        
    def create_file_list(self, parent):
        """创建文件列表"""
        frame = ttk.Frame(parent)
        parent.add(frame, weight=3)
        
        # 创建文件列表
        columns = ('name', 'size', 'type', 'modified', 'created')
        self.file_list = ttk.Treeview(frame, columns=columns, show='headings')
        
        # 设置列标题
        self.file_list.heading('name', text='名称')
        self.file_list.heading('size', text='大小')
        self.file_list.heading('type', text='类型')
        self.file_list.heading('modified', text='修改时间')
        self.file_list.heading('created', text='创建时间')
        
        # 设置列宽
        self.file_list.column('name', width=200)
        self.file_list.column('size', width=100)
        self.file_list.column('type', width=100)
        self.file_list.column('modified', width=150)
        self.file_list.column('created', width=150)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Treeview",
            font=('Microsoft YaHei UI', 9),
            rowheight=24
        )
        style.configure(
            "Treeview.Heading",
            font=('Microsoft YaHei UI', 9, 'bold')
        )
        
        # 配置标签样式
        self.file_list.tag_configure('folder', foreground='blue')
        self.file_list.tag_configure('file', foreground='black')
        
        # 添加滚动条
        vsb = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.file_list.yview)
        hsb = ttk.Scrollbar(frame, orient=tk.HORIZONTAL, command=self.file_list.xview)
        self.file_list.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # 使用grid布局
        self.file_list.grid(row=0, column=0, sticky='nsew')
        vsb.grid(row=0, column=1, sticky='ns')
        hsb.grid(row=1, column=0, sticky='ew')
        
        # 配置grid权重
        frame.grid_rowconfigure(0, weight=1)
        frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.file_list.bind('<Double-1>', self.on_file_double_click)
        self.file_list.bind('<Button-3>', self.show_context_menu)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        status_bar = ttk.Label(self.window, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def start_file_manager(self):
        """启动文件管理器初始化"""
        async def init():
            try:
                # 发送打开文件管理器命令
                await self.send_command({
                    'command': 'file_manager_open',
                    'client_id': self.client_id
                })
                
                self.status_var.set("正在等待驱动器列表...")
                logger.info("已发送文件管理器初始化命令")
                
            except Exception as e:
                logger.error(f"初始化文件管理器失败: {e}")
                self.show_error("初始化文件管理器失败")
                self.status_var.set("初始化失败")
        
        try:
            # 使用事件循环来运行初始化
            if hasattr(self, 'loop') and self.loop and self.loop.is_running():
                asyncio.run_coroutine_threadsafe(init(), self.loop)
            else:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(init())
        except Exception as e:
            logger.error(f"启动文件管理器失败: {e}")
            self.show_error(f"启动文件管理器失败: {str(e)}")
        
    async def send_command(self, command: dict):
        """发送命令到服务器"""
        try:
            await self.websocket.send(json.dumps(command))
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            raise

    # 实现其他必要的方法...
    def refresh(self):
        """刷新当前目录"""
        try:
            if self.current_path:
                self.navigate_to(self.current_path)
        except Exception as e:
            logger.error(f"刷新操作失败: {e}")
        
    def upload_file(self):
        """上传文件"""
        from tkinter import filedialog
        files = filedialog.askopenfilenames(
            title="选择要上传的文件",
            filetypes=[("所有文件", "*.*")]
        )
        if files:
            for file_path in files:
                asyncio.run(self.send_command({
                    'type': 'file_manager_upload',
                    'client_id': self.client_id,
                    'source_path': file_path,
                    'target_path': self.current_path
                }))

    def download_file(self):
        """下载文件"""
        selected = self.file_list.selection()
        if not selected:
            self.show_error("请选择要下载的文件")
            return
        
        from tkinter import filedialog
        save_path = filedialog.askdirectory(title="选择保存位置")
        if save_path:
            for item in selected:
                file_name = self.file_list.item(item)['values'][0]
                source_path = os.path.join(self.current_path, file_name)
                asyncio.run(self.send_command({
                    'type': 'file_manager_download',
                    'client_id': self.client_id,
                    'source_path': source_path,
                    'target_path': save_path
                }))

    def delete_item(self):
        """删除选中的项目"""
        try:
            selected = self.file_list.selection()
            if not selected:
                self.show_error("请选择要删除的项目")
                return
            
            # 获取选中项目的名称，确保是字符串类型
            items = []
            for item in selected:
                values = self.file_list.item(item)['values']
                if values and values[0] is not None:
                    items.append(str(values[0]))
            
            if not items:
                return
                
            # 确保当前路径是字符串
            if not self.current_path or not isinstance(self.current_path, str):
                self.show_error("当前路径无效")
                return
            
            # 直接执行删除操作
            logger.info(f"开始删除 {len(items)} 个项目")
            for item_name in items:
                try:
                    path = os.path.join(self.current_path, item_name)
                    logger.info(f"删除项目: {path}")
                    asyncio.run(self.send_command({
                        'type': 'file_manager_delete',
                        'client_id': self.client_id,
                        'path': path
                    }))
                except Exception as e:
                    logger.error(f"删除项目失败: {item_name}, 错误: {str(e)}")
            
            # 删除完成后刷新当前目录
            self.refresh()
                
        except Exception as e:
            error_msg = f"删除操作失败: {str(e)}"
            logger.error(error_msg)

    def go_back(self):
        """后退"""
        try:
            if self.current_history_index > 0:
                self.current_history_index -= 1
                path = self.history[self.current_history_index]
                self.navigate_to(path)
                self.update_navigation_buttons()
        except Exception as e:
            logger.error(f"后退操作失败: {e}")
            
    def go_forward(self):
        """前进"""
        try:
            if self.current_history_index < len(self.history) - 1:
                self.current_history_index += 1
                path = self.history[self.current_history_index]
                self.navigate_to(path)
                self.update_navigation_buttons()
        except Exception as e:
            logger.error(f"前进操作失败: {e}")

    def go_up(self):
        """向上一级"""
        try:
            if self.current_path:
                parent_path = os.path.dirname(self.current_path)
                if parent_path != self.current_path:  # 确保不是根目录
                    self.navigate_to(parent_path)
        except Exception as e:
            logger.error(f"向上一级操作失败: {e}")

    def on_path_enter(self, event):
        """处理路径输入"""
        pass
        
    def on_nav_tree_double_click(self, event):
        """处理导航树双击事件"""
        item = self.nav_tree.selection()[0]
        values = self.nav_tree.item(item)['values']
        if values:  # 如果有值，说明是驱动器或目录
            path = values[0]
            self.navigate_to(path)

    def navigate_to(self, path: str):
        """导航到指定路径"""
        async def nav():
            try:
                await self.send_command({
                    'type': 'file_manager_get_directory',
                    'client_id': self.client_id,
                    'path': path
                })
                self.status_var.set(f"正在加载 {path} ...")
            except Exception as e:
                logger.error(f"导航失败: {e}")
                self.show_error(f"导航失败: {str(e)}")
        
        asyncio.run(nav())
        
    def on_file_double_click(self, event):
        """处理文件列表双击事件"""
        try:
            selection = self.file_list.selection()
            if not selection:
                return
            
            item = selection[0]
            values = self.file_list.item(item)['values']
            if not values:
                return
            
            # 确保获取到的值是字符串��型
            name = str(values[0]) if values[0] is not None else ""
            type_str = str(values[2]) if values[2] is not None else ""
            
            logger.info(f"双击项目: {name}, 类型: {type_str}")
            
            if type_str == '文件夹':
                # 如果是文件夹，导航到该目录
                if not self.current_path:
                    logger.error("当前路径为空")
                    return
                    
                new_path = os.path.join(str(self.current_path), name)
                logger.info(f"导航到新路径: {new_path}")
                self.navigate_to(new_path)
            else:
                # 如果是文件，可以添加其他处理...
                logger.info(f"双击文件: {name}")
                pass
                
        except Exception as e:
            logger.error(f"处理双击事件时出错: {str(e)}")
            self.show_error(f"无法打开项目: {str(e)}")

    def create_context_menu(self):
        """创建右键菜单"""
        menu = tk.Menu(self.window, tearoff=0)
        menu.add_command(label="打开", command=self.open_selected)
        menu.add_separator()
        menu.add_command(label="复制", command=self.copy_selected)
        menu.add_command(label="剪切", command=self.cut_selected)
        menu.add_command(label="粘贴", command=self.paste_items)
        menu.add_separator()
        menu.add_command(label="删除", command=self.delete_item)
        menu.add_separator()
        menu.add_command(label="属性", command=self.show_properties)
        return menu

    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.file_list.identify('item', event.x, event.y)
        if item:
            self.file_list.selection_set(item)
            self.context_menu.tk_popup(event.x_root, event.y_root)

    def show_error(self, message: str):
        """显示错误信息"""
        messagebox.showerror("错误", message, parent=self.window)

    def update_drives(self, drives: list):
        """更新驱动器列表"""
        try:
            self.drives = drives
            logger.info(f"开始更新驱动器列表: {drives}")
            
            def update_ui():
                try:
                    # 清空导航树
                    for item in self.nav_tree.get_children():
                        self.nav_tree.delete(item)
                    
                    # 添加"此电脑"节点
                    computer = self.nav_tree.insert("", "end", text="此电脑", open=True)
                    logger.info("添加此电脑节点")
                    
                    # 添加驱动器
                    for drive in drives:
                        try:
                            total = int(drive['total'])
                            free = int(drive['free'])
                            used = total - free
                            used_percent = (used / total) * 100 if total > 0 else 0
                            
                            drive_text = f"{drive['name']} ({self.format_size(free)} 可用，共 {self.format_size(total)})"
                            drive_node = self.nav_tree.insert(
                                computer, 
                                "end",
                                text=drive_text,
                                values=(drive['name'],),
                                tags=('drive',)  # 添加标签以便样式设置
                            )
                            logger.info(f"添加驱动器节点: {drive_text}")
                            
                        except Exception as e:
                            logger.error(f"处理驱动器 {drive.get('name', '未知')} 时出错: {e}")
                            continue
                    
                    # 设置样式
                    self.nav_tree.tag_configure('drive', font=('Microsoft YaHei UI', 9))
                    
                    # 展开"此电脑"节点
                    self.nav_tree.item(computer, open=True)
                    
                    self.status_var.set(f"找到 {len(drives)} 个驱动器")
                    self.initialized = True
                    logger.info("驱动器列表更新完成")
                    
                except Exception as e:
                    error_msg = f"更新驱动器列表UI时出错: {str(e)}"
                    logger.error(error_msg)
                    self.show_error(error_msg)
            
            # 在主线程中执行UI更新
            if self.window.winfo_exists():  # 确保窗口还在
                self.window.after(0, update_ui)
                logger.info("已安排UI更新任务")
            else:
                logger.warning("窗口已关闭，取消UI更新")
                
        except Exception as e:
            error_msg = f"更新驱动器列表失败: {str(e)}"
            logger.error(error_msg)
            self.show_error(error_msg)

    def update_directory_content(self, content: dict):
        """更新目录内容"""
        try:
            logger.info(f"���始更新目录内容: {content}")
            
            # 检查否是导航树的请求
            if content.get('for_nav_tree'):
                logger.info("处理导航树请求")
                self._update_nav_tree_content(content)
                return
            
            logger.info(f"处理文件列表请求: {content.get('path')}")
            
            # 清空文件列表
            for item in self.file_list.get_children():
                self.file_list.delete(item)
            
            # 更新当前路径
            self.current_path = content.get('path', '')
            self.path_var.set(self.current_path)
            
            # 获取文件和目录列表
            items = content.get('items', [])
            if not items:
                logger.warning("目录内容为空")
                self.status_var.set("目录为空")
                return
            
            # 先添加文件夹
            for item in sorted([i for i in items if i['is_dir']], key=lambda x: x['name'].lower()):
                try:
                    self.file_list.insert(
                        "", "end",
                        values=(
                            item['name'],
                            '',
                            '文件夹',
                            item['modified'],
                            item['created']
                        ),
                        tags=('folder',)
                    )
                except Exception as e:
                    logger.error(f"添加文件夹失败: {item['name']}, 错误: {e}")
            
            # 再添加文件
            for item in sorted([i for i in items if not i['is_dir']], key=lambda x: x['name'].lower()):
                try:
                    self.file_list.insert(
                        "", "end",
                        values=(
                            item['name'],
                            self.format_size(item['size']),
                            self.get_file_type(item['name']),
                            item['modified'],
                            item['created']
                        ),
                        tags=('file',)
                    )
                except Exception as e:
                    logger.error(f"添加文件失败: {item['name']}, 错误: {e}")
            
            # 更新状态栏
            total_items = len(items)
            folders = sum(1 for i in items if i['is_dir'])
            files = total_items - folders
            self.status_var.set(f"共 {total_items} 个项目 ({folders} 个文件夹, {files} 个文件)")
            
            # 添加到历史记录
            self.add_to_history(self.current_path)
            
            logger.info(f"目录内容更新完成: {self.current_path}")
            
        except Exception as e:
            error_msg = f"更新目录内容失败: {str(e)}"
            logger.error(error_msg)
            self.show_error(error_msg)

    def _update_nav_tree_content(self, content: dict):
        """更新导航树内容"""
        try:
            tree_item = content.get('tree_item')
            items = content.get('items', [])
            
            # 只添加文件夹
            folders = sorted([i for i in items if i['is_dir']], key=lambda x: x['name'].lower())
            
            # 清除现有的子节点
            for child in self.nav_tree.get_children(tree_item):
                self.nav_tree.delete(child)
            
            # 添加文件夹节点
            for folder in folders:
                try:
                    folder_path = os.path.join(content['path'], folder['name'])
                    self.nav_tree.insert(
                        tree_item,
                        'end',
                        text=folder['name'],
                        values=(folder_path,),
                        tags=('folder',)
                    )
                except Exception as e:
                    logger.error(f"添加文件夹节点失败: {folder['name']}, 错误: {e}")
            
            logger.info(f"导航树节点更新完成: {content['path']}")
            
        except Exception as e:
            logger.error(f"更新导航树内容失败: {e}")

    def _update_file_list_content(self, content: dict):
        """更新文件列表内容"""
        try:
            # 清空文件列表
            for item in self.file_list.get_children():
                self.file_list.delete(item)
            
            # 更新当前路径
            self.current_path = content.get('path', '')
            self.path_var.set(self.current_path)
            
            # 获取文件和目录列表
            items = content.get('items', [])
            if not items:
                logger.warning("目录内容为空")
                self.status_var.set("目录为空")
                return
            
            def update_ui():
                try:
                    # 先添加文件夹
                    for item in sorted([i for i in items if i['is_dir']], key=lambda x: x['name'].lower()):
                        try:
                            self.file_list.insert(
                                "", "end",
                               values=(
                                    item['name'],
                                    '',
                                    '文件夹',
                                    item['modified'],
                                    item['created']
                                ),
                                tags=('folder',)
                            )
                        except Exception as e:
                            logger.error(f"添加文件夹失败: {item['name']}, 错误: {e}")
                    
                    # 再添加文件
                    for item in sorted([i for i in items if not i['is_dir']], key=lambda x: x['name'].lower()):
                        try:
                            self.file_list.insert(
                                "", "end",
                               values=(
                                    item['name'],
                                    self.format_size(item['size']),
                                    self.get_file_type(item['name']),
                                    item['modified'],
                                    item['created']
                                ),
                                tags=('file',)
                            )
                        except Exception as e:
                            logger.error(f"添加文件失败: {item['name']}, 错误: {e}")
                           
            # 更新状态栏
                    total_items = len(items)
                    folders = sum(1 for i in items if i['is_dir'])
                    files = total_items - folders
                    self.status_var.set(f"共 {total_items} 个项目 ({folders} 个文件夹, {files} 个文件)")
                    
                except Exception as e:
                    logger.error(f"更新UI时出错: {e}")
                    self.show_error(f"更新UI时出错: {str(e)}")
            
            # 在主线程中执行UI更新
            if self.window.winfo_exists():
                self.window.after(0, update_ui)
            else:
                logger.warning("窗口已关闭，取消更新")
            
        except Exception as e:
            logger.error(f"更新文件列表内容失败: {e}")

    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        try:
            size = float(size)  # 确保size是数字
            for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
                if size < 1024:
                    return f"{size:.1f} {unit}"
                size /= 1024
            return f"{size:.1f} PB"
        except Exception as e:
            logger.error(f"格式化大小失败: {e}")
            return "未知大���"

    def get_file_type(self, filename: str) -> str:
        """获取文件类型"""
        ext = os.path.splitext(filename)[1].lower()
        type_map = {
            '.txt': '文本文档',
            '.doc': 'Word文档',
            '.docx': 'Word文档',
            '.pdf': 'PDF文档',
            '.jpg': 'JPEG图片',
            '.png': 'PNG图片',
            '.exe': '应用程序'
        }
        return type_map.get(ext, ext[1:].upper() if ext else '文件')

    def on_tree_open(self, event):
        """处理树节点展开事件"""
        item = self.nav_tree.focus()
        if not item:
            return
        
        # 获取节点信息
        item_data = self.nav_tree.item(item)
        values = item_data.get('values', [])
        if not values:
            return
        
        path = values[0]
        logger.info(f"展开节点: {path}")
        
        # 如果节点已经有子节点，不重新加载
        if self.nav_tree.get_children(item):
            return
        
        # 获取目录内容
        async def get_content():
            try:
                await self.send_command({
                    'type': 'file_manager_get_directory',
                    'client_id': self.client_id,
                    'path': path,
                    'for_nav_tree': True,
                    'tree_item': item
                })
                self.status_var.set(f"正在加载 {path} 的子目录...")
            except Exception as e:
                logger.error(f"获取目录内容失败: {e}")
                self.show_error(f"获取目录内容失败: {str(e)}")
        
        asyncio.run(get_content())

    def add_to_history(self, path: str):
        """添加路径到历史记录"""
        try:
            # 如果当前不在历史记录末尾，删除当前位置之后的记录
            if self.current_history_index < len(self.history) - 1:
                self.history = self.history[:self.current_history_index + 1]
                
            # 如果路径与当前路径相同，不添加到历史记录
            if self.history and self.history[-1] == path:
                return
                
            self.history.append(path)
            self.current_history_index = len(self.history) - 1
            
            # 更新导航按钮状态
            self.update_navigation_buttons()
            
        except Exception as e:
            logger.error(f"添加历史记录失败: {e}")

    def update_navigation_buttons(self):
        """更新导航按钮状态"""
        try:
            if hasattr(self, 'back_btn') and hasattr(self, 'forward_btn'):
                self.back_btn.configure(state='normal' if self.current_history_index > 0 else 'disabled')
                self.forward_btn.configure(state='normal' if self.current_history_index < len(self.history) - 1 else 'disabled')
        except Exception as e:
            logger.error(f"更新导航按钮状态失败: {e}")

    def show_properties(self):
        """显示属性对话框"""
        selected = self.file_list.selection()
        if not selected:
            return
            
        item = selected[0]
        values = self.file_list.item(item)['values']
        name = values[0]
        
        # 创建属性对话框
        dialog = tk.Toplevel(self.window)
        dialog.title(f"{name} 的属性")
        dialog.geometry("400x300")
        dialog.transient(self.window)
        dialog.grab_set()
        
        # 添加属性信息
        ttk.Label(dialog, text=f"名称: {name}").pack(anchor=tk.W, padx=10, pady=5)
        ttk.Label(dialog, text=f"类型: {values[2]}").pack(anchor=tk.W, padx=10, pady=5)
        ttk.Label(dialog, text=f"大小: {values[1]}").pack(anchor=tk.W, padx=10, pady=5)
        ttk.Label(dialog, text=f"修改时间: {values[3]}").pack(anchor=tk.W, padx=10, pady=5)
        ttk.Label(dialog, text=f"创建时间: {values[4]}").pack(anchor=tk.W, padx=10, pady=5)
        ttk.Label(dialog, text=f"位置: {self.current_path}").pack(anchor=tk.W, padx=10, pady=5)
        
        # 添加确定按钮
        ttk.Button(dialog, text="确定", command=dialog.destroy).pack(pady=10)

    def open_selected(self):
        """打开选中的项目"""
        selected = self.file_list.selection()
        if not selected:
            return
            
        item = selected[0]
        values = self.file_list.item(item)['values']
        name = values[0]
        type_str = values[2]
        
        if type_str == '文件夹':
            # 如果是文件夹，导航到该目录
            new_path = os.path.join(self.current_path, name)
            self.navigate_to(new_path)
        else:
            # 如果是文件，可以添加打开文件的功能
            pass

    def copy_selected(self):
        """复制选中的项目"""
        selected = self.file_list.selection()
        if not selected:
            return
        
        # 保存选中的项目信息，用于后续粘贴
        self.clipboard = []
        for item in selected:
            values = self.file_list.item(item)['values']
            self.clipboard.append({
                'name': values[0],
                'path': os.path.join(self.current_path, values[0]),
                'action': 'copy'
            })
        
        self.status_var.set(f"已复制 {len(self.clipboard)} 个项目")

    def cut_selected(self):
        """剪切选中的项目"""
        selected = self.file_list.selection()
        if not selected:
            return
        
        # 保存选中的项目信息，用于后续粘贴
        self.clipboard = []
        for item in selected:
            values = self.file_list.item(item)['values']
            self.clipboard.append({
                'name': values[0],
                'path': os.path.join(self.current_path, values[0]),
                'action': 'cut'
            })
        
        self.status_var.set(f"已剪切 {len(self.clipboard)} 个项目")

    def paste_items(self):
        """粘贴项目"""
        if not hasattr(self, 'clipboard') or not self.clipboard:
            return
        
        for item in self.clipboard:
            source_path = item['path']
            target_path = os.path.join(self.current_path, item['name'])
            
            asyncio.run(self.send_command({
                'type': 'file_manager_paste',
                'client_id': self.client_id,
                'source_path': source_path,
                'target_path': target_path,
                'action': item['action']
            }))
        
        # 清空剪贴板
        if any(item['action'] == 'cut' for item in self.clipboard):
            self.clipboard = []