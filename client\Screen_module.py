import asyncio
import json
import logging
import mss
import numpy as np
import cv2
import time
import base64
from concurrent.futures import ThreadPoolExecutor
import os
import websockets

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ScreenSharer:
    def __init__(self):
        self.sct = mss.mss()
        self.monitor = self.sct.monitors[1]  # 主显示器
        
        # 压缩和性能设置
        self.jpeg_quality = 70
        self.frame_skip = 2
        self.resolution_scale = 0.8
        self.target_fps = 15
        self.settings = {
            'resolution': {
                '480p': (854, 480),
                '720p': (1280, 720),
                '1080p': (1920, 1080)
            },
            'fps': [5, 15, 30],
            'quality': {
                '高': {
                    'bitrate': 2000000,      # 2Mbps
                    'block_size': 64,       # 较小的块大小，更精细的检测
                    'compression': 80,        # 较高的图像质量
                    'motion_threshold': 50,   # 提高运动阈值，减少误判
                    'min_changed_pixels': 200, # 提高变化像素阈值
                    'max_regions': 4,        # 减少区域数量
                    'grid_size': 2,         # 减少网格数量
                    'check_interval': 0.1,   # 检测间隔(秒)
                    'max_total_area_ratio': 0.1
                },
                '中': {
                    'bitrate': 1000000,      # 1Mbps
                    'block_size': 128,       # 中等块大小
                    'compression': 40,        # 中等图像质量
                    'motion_threshold': 60,   # 提高运动阈值
                    'min_changed_pixels': 400, # 提高变化像素阈值
                    'max_regions': 3,        # 减少区域数量
                    'grid_size': 2,         # 减少网格数量
                    'check_interval': 0.3,   # 检测间隔(秒)
                    'max_total_area_ratio': 0.3
                },
                '低': {
                    'bitrate': 100000,       # 100Kbps
                    'block_size': 256,       # 较大的块大小
                    'compression': 10,        # 较低的图像质量
                    'motion_threshold': 70,   # 提高运动阈值
                    'min_changed_pixels': 800, # 提高变化像素阈值
                    'max_regions': 2,        # 减少区域数量
                    'grid_size': 2,         # 减少网格数量
                    'check_interval': 0.5,   # 检测间隔(秒)
                    'max_total_area_ratio': 0.5
                }
            }
        }
        
        # 当前设置
        self.current_settings = {
            'resolutions': ["720p"],
            'fps_values': [15],
            'quality': '中'
        }
        self.update_quality_settings('中')
        
        # 状态控制
        self.running = False
        self.last_frame_time = 0
        
        # 线程池用于并行处理
        self.executor = ThreadPoolExecutor(max_workers=4)
    def update_quality_settings(self, quality):
        """更新与质量相关的所有设置"""
        quality_settings = self.settings['quality'][quality]
        # 更新所有与质量相关的参数
        for key, value in quality_settings.items():
            self.current_settings[key] = value
        logger.info(f"已更新质量相关设置: {quality}")
        logger.debug(f"当前设置: {self.current_settings}")
    def compress_frame(self, frame):
        """压缩帧数据"""
        try:
            # 将帧编码为JPEG格式
            success, encoded_image = cv2.imencode('.jpg', frame)
            
            if not success:
                raise Exception("编码图像失败")

            # 转换为字节数据并进行base64编码
            compressed_data = base64.b64encode(encoded_image.tobytes()).decode('utf-8')

            return compressed_data
        
        except Exception as e:
            logger.error(f"压缩帧数据失败: {e}")
            return None
    async def start_share(self, websocket):
        """启动屏幕共享"""
        self.running = True
        logger.info("屏幕共享已启动")
        
        try:
            while self.running:
                try:
                    # 控制帧率
                    current_time = time.time()
                    if current_time - self.last_frame_time < 1.0 / self.target_fps:
                        await asyncio.sleep(0.001)
                        continue
                    
                    # 捕获屏幕
                    frame = self.capture_screen()
                    if frame is None:
                        continue
                    
                    # 分块并发送
                    await self.send_full_frame_parallel(frame, websocket)
                    self.last_frame_time = current_time
                    
                except Exception as e:
                    logger.error(f"屏幕共享过程错误: {e}")
                    await asyncio.sleep(1)
                    
        except Exception as e:
            logger.error(f"屏幕共享主循环错误: {e}")
        finally:
            self.running = False
            self.executor.shutdown(wait=False)
            logger.info("屏幕共享已停止")

    def capture_screen(self):
        """捕获屏幕"""
        try:
            screenshot = self.sct.grab(self.monitor)
            frame = np.array(screenshot)
            
            # 调整大小以减少数据量
            height, width = frame.shape[:2]
            new_size = (int(width * self.resolution_scale), 
                       int(height * self.resolution_scale))
            frame = cv2.resize(frame, new_size, interpolation=cv2.INTER_AREA)
            
            return frame
        except Exception as e:
            logger.error(f"截图错误: {e}")
            return None

    def split_frame_into_blocks(self, frame, grid_size=4):
        """将帧分割成网格块，使用更大的块和更严格的差异检测"""
        height, width = frame.shape[:2]
        block_h = height // grid_size
        block_w = width // grid_size
        blocks = []
        
        # 临时禁用检查间隔限制，让屏幕共享能正常工作
        # current_time = time.time()
        # if current_time - self.last_frame_time < self.current_settings['check_interval']:
        #     return []
        
        # self.last_frame_time = current_time
        
        if not hasattr(self, 'previous_blocks'):
            self.previous_blocks = {}
        
        for i in range(grid_size):
            for j in range(grid_size):
                y1 = i * block_h
                y2 = min((i + 1) * block_h, height)
                x1 = j * block_w
                x2 = min((j + 1) * block_w, width)
                
                block = frame[y1:y2, x1:x2]
                block_id = f"{i},{j}"
                
                # 临时禁用差异检测，总是发送所有块
                # if block_id in self.previous_blocks:
                #     prev_block = self.previous_blocks[block_id]
                #     if prev_block.shape == block.shape:
                #         # 使用更严格的差异检测
                #         diff = cv2.absdiff(block, prev_block)
                #         changed_pixels = np.sum(diff > self.current_settings['motion_threshold'])
                #         if changed_pixels > self.current_settings['min_changed_pixels']:
                #             self.previous_blocks[block_id] = block.copy()
                #             blocks.append({
                #                 'data': block,
                #                 'x': x1,
                #                 'y': y1,
                #                 'width': x2 - x1,
                #                 'height': y2 - y1,
                #                 'position': (i * grid_size + j)
                #             })
                # else:
                #     self.previous_blocks[block_id] = block.copy()
                #     blocks.append({
                #         'data': block,
                #         'x': x1,
                #         'y': y1,
                #         'width': x2 - x1,
                #         'height': y2 - y1,
                #         'position': (i * grid_size + j)
                #     })
                
                # 总是发送所有块（临时调试）
                self.previous_blocks[block_id] = block.copy()
                blocks.append({
                    'data': block,
                    'x': x1,
                    'y': y1,
                    'width': x2 - x1,
                    'height': y2 - y1,
                    'position': (i * grid_size + j)
                })
        
        return blocks

    def compress_block(self, block):
        """压缩单个块"""
        try:
            quality = self.settings['quality'][self.current_settings['quality']]['compression']
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            _, encoded_img = cv2.imencode('.jpg', block['data'], encode_param)
            
            # 计算块的复杂度（用于优先级排序）
            complexity = cv2.Laplacian(block['data'], cv2.CV_64F).var()
            
            return {
                'x': block['x'],
                'y': block['y'],
                'width': block['width'],
                'height': block['height'],
                'position': block['position'],
                'complexity': complexity,
                'data': encoded_img.tobytes()  # 直接返回二进制数据，不进行base64编码
            }
        except Exception as e:
            logger.error(f"压缩块时发生错误: {e}")
            return None

    def prioritize_blocks(self, blocks):
        """对块进行优先级排序"""
        # 移除压缩失败的块
        blocks = [b for b in blocks if b is not None]
        
        # 根据位置和复杂度进行排序
        # 中心区域优先级高，复杂度大的区域优先级也高
        blocks.sort(key=lambda b: (-b['complexity'], b['position']))
        return blocks
    def _calculate_scale_ratio(self, width, height):
        """计算缩放比例"""
        # 获取目标分辨率
        target_res = self.settings['resolution'][self.current_settings['resolutions'][0]]
        
        # 计算宽高比
        src_ratio = width / height
        target_ratio = target_res[0] / target_res[1]
        
        if src_ratio > target_ratio:
            # 源图像更宽，以高度为基准
            scale = target_res[1] / height
        else:
            # 源图像更高，以宽度为基准
            scale = target_res[0] / width
            
        logger.info(f"源分辨率: {width}x{height}, 目标分辨率: {target_res[0]}x{target_res[1]}, 缩放比例: {scale:.3f}")
        return scale
    def update_settings(self, new_settings):
        """更新客户端设置"""
        new_settings = new_settings['settings']
        changes = []
        if 'resolutions' in new_settings:
            old_resolutions = self.current_settings['resolutions']
            resolutions = new_settings['resolutions']
            if resolutions:  # 确保列表非空
                self.current_settings['resolutions'] = resolutions
                changes.append(f'分辨率从 {old_resolutions} 更改为 {resolutions}')
                # 更新缩放比例
                old_ratio = self.resolution_scale
                self.resolution_scale = self._calculate_scale_ratio(
                    self.monitor['width'], 
                    self.monitor['height']
                )
                logger.info(f"缩放比例从 {old_ratio:.3f} 更改为 {self.resolution_scale:.3f}")
                # 强制进行一次全屏刷新
                self.last_full_frame_time = 0
                # 清空之前的块缓存
                if hasattr(self, 'previous_blocks'):
                    self.previous_blocks.clear()
                logger.info("分辨率已更改，将在下一帧进行全屏刷新")

        if 'fps_values' in new_settings:
            old_fps = self.current_settings['fps_values']
            fps_values = new_settings['fps_values']
            if fps_values:  # 确保列表非空
                self.current_settings['fps_values'] = fps_values
                changes.append(f'帧率从 {old_fps} 更改为 {fps_values}')
                
        if 'quality' in new_settings:
            old_quality = self.current_settings['quality']
            quality = new_settings['quality']
            if quality in self.settings['quality']:
                self.current_settings['quality'] = quality
                # 更新所有与质量相关的设置
                self.update_quality_settings(quality)
                changes.append(
                    f'质量从 {old_quality} 更改为 {quality} '
                    f'(比特率: {self.settings["quality"][old_quality]["bitrate"]/1000000:.1f}Mbps->'
                    f'{self.settings["quality"][quality]["bitrate"]/1000000:.1f}Mbps)'
                )
        
        if changes:
            logger.info("===== 设置更新 =====")
            for change in changes:
                logger.info(change)
            logger.info("==================")
            return True  # 表示设置已更新
        return False  # 表示没有设置更新
                    
    async def send_full_frame_parallel(self, frame, websocket, client_id):
        """并行处理并发送完整画面"""
        try:
            frame = cv2.resize(frame, self.settings['resolution'][self.current_settings['resolutions'][0]])
            # 1. 分块
            blocks = self.split_frame_into_blocks(frame, grid_size=4)
            
            # 2. 并行压缩
            compressed_blocks = list(self.executor.map(self.compress_block, blocks))
            
            # 3. 优先级排序
            prioritized_blocks = self.prioritize_blocks(compressed_blocks)
            
            # 4. 异步发送
            for block in prioritized_blocks:
                try:
                    # 构建消息头
                    header = {
                    }
                    # 发送消息
                    message = {
                        'x': int(block['x']),
                        'y': int(block['y']),
                        'width': int(block['width']),
                        'height': int(block['height']),
                        'encoding': 'jpeg',
                        'type': 'screen_data',
                        'header': header,
                        'data': base64.b64encode(block['data']).decode('utf-8'),
                        "client_id": client_id,
                    }
                    await websocket.send(json.dumps(message))
                    
                except Exception as e:
                    logger.error(f"发送块时出错: {e}")
                    
        except Exception as e:
            logger.error(f"发送帧时出错: {e}")

    def stop(self):
        """停止屏幕共享"""
        self.running = False