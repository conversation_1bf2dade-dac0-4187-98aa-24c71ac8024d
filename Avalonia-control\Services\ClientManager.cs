using AvaloniaControlCenter.Models;
using Serilog;
using System;
using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace AvaloniaControlCenter.Services;

/// <summary>
/// 客户端管理服务
/// </summary>
public class ClientManager
{
    private readonly ILogger _logger;
    private readonly WebSocketService _webSocketService;
    private readonly DispatcherQueue _dispatcherQueue;
    private readonly ConcurrentDictionary<string, ClientModel> _clients = new();
    private readonly ConcurrentDictionary<string, GroupModel> _groups = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="webSocketService">WebSocket服务</param>
    /// <param name="dispatcherQueue">调度队列</param>
    public ClientManager(ILogger logger, WebSocketService webSocketService, DispatcherQueue dispatcherQueue)
    {
        _logger = logger;
        _webSocketService = webSocketService;
        _dispatcherQueue = dispatcherQueue;

        // 订阅WebSocket消息
        _webSocketService.MessageReceived += OnMessageReceived;

        // 初始化默认分组
        InitializeDefaultGroup();
    }

    /// <summary>
    /// 客户端列表
    /// </summary>
    public ObservableCollection<ClientModel> Clients { get; } = new();

    /// <summary>
    /// 分组列表
    /// </summary>
    public ObservableCollection<GroupModel> Groups { get; } = new();

    /// <summary>
    /// 在线客户端数量
    /// </summary>
    public int OnlineCount => _clients.Values.Count(c => c.Status == ClientStatus.Online);

    /// <summary>
    /// 总客户端数量
    /// </summary>
    public int TotalCount => _clients.Count;

    /// <summary>
    /// 客户端状态变更事件
    /// </summary>
    public event EventHandler<ClientModel>? ClientStatusChanged;

    /// <summary>
    /// 客户端添加事件
    /// </summary>
    public event EventHandler<ClientModel>? ClientAdded;

    /// <summary>
    /// 客户端移除事件
    /// </summary>
    public event EventHandler<ClientModel>? ClientRemoved;

    /// <summary>
    /// 统计信息变更事件
    /// </summary>
    public event EventHandler? StatisticsChanged;

    /// <summary>
    /// 获取客户端
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>客户端模型</returns>
    public ClientModel? GetClient(string clientId)
    {
        return _clients.TryGetValue(clientId, out var client) ? client : null;
    }

    /// <summary>
    /// 获取选中的客户端
    /// </summary>
    /// <returns>选中的客户端列表</returns>
    public ClientModel[] GetSelectedClients()
    {
        return _clients.Values.Where(c => c.IsSelected).ToArray();
    }

    /// <summary>
    /// 发送命令到客户端
    /// </summary>
    /// <param name="command">命令</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>发送任务</returns>
    public async Task SendCommandAsync(string command, string clientId)
    {
        var commandModel = CommandModel.CreateCommand(command, clientId);
        await _webSocketService.SendCommandAsync(commandModel);
        _logger.Information("发送命令到客户端 {ClientId}: {Command}", clientId, command);
    }

    /// <summary>
    /// 广播命令到所有在线客户端
    /// </summary>
    /// <param name="command">命令</param>
    /// <returns>发送任务</returns>
    public async Task BroadcastCommandAsync(string command)
    {
        var onlineClients = _clients.Values.Where(c => c.Status == ClientStatus.Online).ToArray();
        
        foreach (var client in onlineClients)
        {
            await SendCommandAsync(command, client.ClientId);
        }

        _logger.Information("广播命令到 {Count} 个在线客户端: {Command}", onlineClients.Length, command);
    }

    /// <summary>
    /// 发送命令到选中的客户端
    /// </summary>
    /// <param name="command">命令</param>
    /// <returns>发送任务</returns>
    public async Task SendCommandToSelectedAsync(string command)
    {
        var selectedClients = GetSelectedClients();
        
        foreach (var client in selectedClients)
        {
            await SendCommandAsync(command, client.ClientId);
        }

        _logger.Information("发送命令到 {Count} 个选中客户端: {Command}", selectedClients.Length, command);
    }

    /// <summary>
    /// 添加或更新客户端
    /// </summary>
    /// <param name="client">客户端模型</param>
    private void AddOrUpdateClient(ClientModel client)
    {
        var isNewClient = !_clients.ContainsKey(client.ClientId);
        var oldStatus = isNewClient ? ClientStatus.Offline : _clients[client.ClientId].Status;

        _clients.AddOrUpdate(client.ClientId, client, (key, existing) =>
        {
            // 保留选中状态
            client.IsSelected = existing.IsSelected;
            return client;
        });

        // 添加到分组
        var group = GetOrCreateGroup(client.GroupName);
        var existingInGroup = group.FindClient(client.ClientId);
        if (existingInGroup == null)
        {
            group.AddClient(client);
        }
        else
        {
            // 更新分组中的客户端信息
            var index = group.Clients.IndexOf(existingInGroup);
            if (index >= 0)
            {
                group.Clients[index] = client;
            }
        }

        // 使用调度队列更新UI
        _dispatcherQueue.EnqueueUpdate(() =>
        {
            if (isNewClient)
            {
                Clients.Add(client);
                ClientAdded?.Invoke(this, client);
            }
            else
            {
                var index = Clients.ToList().FindIndex(c => c.ClientId == client.ClientId);
                if (index >= 0)
                {
                    Clients[index] = client;
                }
            }

            // 如果状态发生变化，触发事件
            if (oldStatus != client.Status)
            {
                ClientStatusChanged?.Invoke(this, client);
                StatisticsChanged?.Invoke(this, EventArgs.Empty);
            }
        });

        _logger.Debug("客户端 {ClientId} 状态更新: {Status}", client.ClientId, client.Status);
    }

    /// <summary>
    /// 移除客户端
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    private void RemoveClient(string clientId)
    {
        if (_clients.TryRemove(clientId, out var client))
        {
            // 从分组中移除
            var group = _groups.Values.FirstOrDefault(g => g.FindClient(clientId) != null);
            group?.RemoveClient(clientId);

            _dispatcherQueue.EnqueueUpdate(() =>
            {
                var clientToRemove = Clients.FirstOrDefault(c => c.ClientId == clientId);
                if (clientToRemove != null)
                {
                    Clients.Remove(clientToRemove);
                    ClientRemoved?.Invoke(this, client);
                    StatisticsChanged?.Invoke(this, EventArgs.Empty);
                }
            });

            _logger.Information("客户端 {ClientId} 已移除", clientId);
        }
    }

    /// <summary>
    /// 获取或创建分组
    /// </summary>
    /// <param name="groupName">分组名称</param>
    /// <returns>分组模型</returns>
    private GroupModel GetOrCreateGroup(string groupName)
    {
        return _groups.GetOrAdd(groupName, name =>
        {
            var group = new GroupModel { Name = name };
            
            _dispatcherQueue.EnqueueUpdate(() =>
            {
                Groups.Add(group);
            });

            return group;
        });
    }

    /// <summary>
    /// 初始化默认分组
    /// </summary>
    private void InitializeDefaultGroup()
    {
        GetOrCreateGroup("默认分组");
    }

    /// <summary>
    /// 处理WebSocket消息
    /// </summary>
    private void OnMessageReceived(object? sender, string message)
    {
        try
        {
            var jsonDocument = JsonDocument.Parse(message);
            var root = jsonDocument.RootElement;

            if (!root.TryGetProperty("type", out var typeElement))
                return;

            var messageType = typeElement.GetString();

            switch (messageType)
            {
                case "client_status_change":
                    HandleClientStatusChange(root);
                    break;
                case "initial_status":
                    HandleInitialStatus(root);
                    break;
                case "command_response":
                    HandleCommandResponse(root);
                    break;
                case "heartbeat":
                    HandleHeartbeat(root);
                    break;
                default:
                    _logger.Debug("收到未知消息类型: {MessageType}", messageType);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "处理WebSocket消息时发生错误: {Message}", message);
        }
    }

    /// <summary>
    /// 处理客户端状态变更
    /// </summary>
    private void HandleClientStatusChange(JsonElement root)
    {
        if (root.TryGetProperty("client", out var clientElement))
        {
            var client = JsonSerializer.Deserialize<ClientModel>(clientElement.GetRawText());
            if (client != null)
            {
                AddOrUpdateClient(client);
            }
        }
    }

    /// <summary>
    /// 处理初始状态
    /// </summary>
    private void HandleInitialStatus(JsonElement root)
    {
        if (root.TryGetProperty("clients", out var clientsElement))
        {
            var clients = JsonSerializer.Deserialize<ClientModel[]>(clientsElement.GetRawText());
            if (clients != null)
            {
                foreach (var client in clients)
                {
                    AddOrUpdateClient(client);
                }
            }
        }
    }

    /// <summary>
    /// 处理命令响应
    /// </summary>
    private void HandleCommandResponse(JsonElement root)
    {
        // TODO: 处理命令响应
        _logger.Debug("收到命令响应: {Response}", root.GetRawText());
    }

    /// <summary>
    /// 处理心跳
    /// </summary>
    private void HandleHeartbeat(JsonElement root)
    {
        if (root.TryGetProperty("client_id", out var clientIdElement))
        {
            var clientId = clientIdElement.GetString();
            if (!string.IsNullOrEmpty(clientId) && _clients.TryGetValue(clientId, out var client))
            {
                client.LastHeartbeat = DateTime.Now;
                client.Status = ClientStatus.Online;
                AddOrUpdateClient(client);
            }
        }
    }
}
