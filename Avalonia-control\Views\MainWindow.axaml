<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:AvaloniaControlCenter.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1400" d:DesignHeight="900"
        x:Class="AvaloniaControlCenter.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Icon="/Assets/icon.ico"
        Title="{Binding Title}"
        Width="1400" Height="900"
        MinWidth="1200" MinHeight="700"
        WindowStartupLocation="CenterScreen">

    <Design.DataContext>
        <!-- This only sets the DataContext for the previewer in an IDE,
             to set the actual DataContext for runtime, set the DataContext property in code (look at App.axaml.cs) -->
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <Grid RowDefinitions="Auto,*,Auto">
        
        <!-- 顶部工具栏 -->
        <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
            <Grid ColumnDefinitions="Auto,*,Auto">
                
                <!-- 连接控制 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="10">
                    <TextBlock Text="服务器:" VerticalAlignment="Center"/>
                    <TextBox Text="{Binding ServerUrl}" Width="200" VerticalAlignment="Center"/>
                    <Button Content="连接" Command="{Binding ConnectCommand}" 
                            IsEnabled="{Binding !IsConnected}" Classes="primary"/>
                    <Button Content="断开" Command="{Binding DisconnectCommand}" 
                            IsEnabled="{Binding IsConnected}" Classes="danger"/>
                    <TextBlock Text="{Binding ConnectionStatus}" VerticalAlignment="Center"
                               Foreground="{Binding IsConnected, Converter={StaticResource BooleanToColorConverter}}"/>
                </StackPanel>

                <!-- 命令输入 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="10">
                    <TextBlock Text="命令:" VerticalAlignment="Center"/>
                    <TextBox Text="{Binding CommandText}" Width="300" VerticalAlignment="Center" 
                             Watermark="输入命令..."/>
                    <Button Content="发送到选中" Command="{Binding SendCommandCommand}" 
                            IsEnabled="{Binding IsConnected}"/>
                    <Button Content="广播到所有" Command="{Binding BroadcastCommandCommand}" 
                            IsEnabled="{Binding IsConnected}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" ColumnDefinitions="*,Auto,300">
            
            <!-- 客户端列表 -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <Grid RowDefinitions="Auto,*">
                    
                    <!-- 过滤控制 -->
                    <Border Grid.Row="0" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
                        <Grid ColumnDefinitions="*,Auto,Auto,Auto">
                            <TextBox Grid.Column="0" Text="{Binding ClientListViewModel.SearchText}" 
                                     Watermark="搜索客户端..." Margin="0,0,10,0"/>
                            <ComboBox Grid.Column="1" SelectedItem="{Binding ClientListViewModel.SelectedGroupName}"
                                      ItemsSource="{Binding ClientListViewModel.GroupNames}" 
                                      Width="120" Margin="0,0,10,0"/>
                            <CheckBox Grid.Column="2" Content="仅在线" 
                                      IsChecked="{Binding ClientListViewModel.ShowOnlineOnly}" 
                                      Margin="0,0,10,0"/>
                            <Button Grid.Column="3" Content="刷新" 
                                    Command="{Binding ClientListViewModel.RefreshCommand}"/>
                        </Grid>
                    </Border>

                    <!-- 客户端列表 -->
                    <ScrollViewer Grid.Row="1">
                        <ItemsControl ItemsSource="{Binding ClientListViewModel.FilteredClients}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Orientation="Vertical"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate DataType="vm:ClientItemViewModel">
                                    <Border Classes="client-item" Classes.selected="{Binding IsSelected}"
                                            Margin="2" CornerRadius="4">
                                        <Grid ColumnDefinitions="Auto,*,Auto" Margin="8" RowDefinitions="Auto,Auto">

                                            <!-- 左侧：状态指示器和选择框 -->
                                            <StackPanel Grid.Column="0" Grid.RowSpan="2" Orientation="Vertical"
                                                        VerticalAlignment="Center" Spacing="5" Margin="0,0,12,0">
                                                <Ellipse Width="14" Height="14" Fill="{Binding StatusColor}"/>
                                                <CheckBox IsChecked="{Binding IsSelected}"
                                                          Command="{Binding SelectCommand}"
                                                          HorizontalAlignment="Center"/>
                                            </StackPanel>

                                            <!-- 中间：客户端详细信息 -->
                                            <StackPanel Grid.Column="1" Grid.Row="0" Spacing="4">
                                                <!-- 第一行：主要信息 -->
                                                <Grid ColumnDefinitions="*,Auto,Auto">
                                                    <TextBlock Grid.Column="0" Text="{Binding DisplayName}"
                                                               FontWeight="Bold" FontSize="13"
                                                               VerticalAlignment="Center"/>
                                                    <Border Grid.Column="1" Background="{Binding StatusColor}"
                                                            CornerRadius="8" Padding="6,2" Margin="8,0">
                                                        <TextBlock Text="{Binding StatusText}"
                                                                   Foreground="White" FontSize="10" FontWeight="Bold"/>
                                                    </Border>
                                                    <TextBlock Grid.Column="2" Text="{Binding LastSeenDisplay}"
                                                               FontSize="10" Foreground="Gray"
                                                               VerticalAlignment="Center"/>
                                                </Grid>

                                                <!-- 第二行：系统信息 -->
                                                <Grid ColumnDefinitions="Auto,*,Auto,*">
                                                    <TextBlock Grid.Column="0" Text="IP:" FontSize="10"
                                                               Foreground="Gray" Margin="0,0,4,0"/>
                                                    <TextBlock Grid.Column="1" Text="{Binding IpAddress}"
                                                               FontSize="10" Foreground="DarkBlue"
                                                               Margin="0,0,16,0"/>
                                                    <TextBlock Grid.Column="2" Text="ID:" FontSize="10"
                                                               Foreground="Gray" Margin="0,0,4,0"/>
                                                    <TextBlock Grid.Column="3" Text="{Binding ClientId}"
                                                               FontSize="10" Foreground="Gray"
                                                               TextTrimming="CharacterEllipsis"/>
                                                </Grid>

                                                <!-- 第三行：硬件信息 -->
                                                <Grid ColumnDefinitions="Auto,*">
                                                    <TextBlock Grid.Column="0" Text="系统:" FontSize="10"
                                                               Foreground="Gray" Margin="0,0,4,0"/>
                                                    <TextBlock Grid.Column="1" Text="{Binding OsInfo}"
                                                               FontSize="10" Foreground="Gray"
                                                               TextTrimming="CharacterEllipsis"/>
                                                </Grid>

                                                <!-- 第四行：CPU和内存 -->
                                                <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                                                    <TextBlock Grid.Column="0" Text="CPU:" FontSize="10"
                                                               Foreground="Gray" Margin="0,0,4,0"/>
                                                    <TextBlock Grid.Column="1" Text="{Binding CpuInfo}"
                                                               FontSize="10" Foreground="Gray"
                                                               TextTrimming="CharacterEllipsis" Margin="0,0,16,0"/>
                                                    <TextBlock Grid.Column="2" Text="内存:" FontSize="10"
                                                               Foreground="Gray" Margin="0,0,4,0"/>
                                                    <TextBlock Grid.Column="3" Text="{Binding Memory}"
                                                               FontSize="10" Foreground="Gray"/>
                                                </Grid>
                                            </StackPanel>

                                            <!-- 右侧：操作按钮 -->
                                            <StackPanel Grid.Column="2" Grid.RowSpan="2" Orientation="Vertical"
                                                        VerticalAlignment="Center" Spacing="4">
                                                <Button Content="🖥️ 屏幕" FontSize="10" Padding="8,4"
                                                        Command="{Binding OpenScreenShareCommand}"
                                                        IsEnabled="{Binding IsOnline}"
                                                        Classes="primary" MinWidth="60"/>
                                                <Button Content="📁 文件" FontSize="10" Padding="8,4"
                                                        Command="{Binding OpenFileManagerCommand}"
                                                        IsEnabled="{Binding IsOnline}" MinWidth="60"/>
                                                <Button Content="⌨️ CMD" FontSize="10" Padding="8,4"
                                                        Command="{Binding OpenCmdCommand}"
                                                        IsEnabled="{Binding IsOnline}" MinWidth="60"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" Width="5" Background="#E0E0E0"/>

            <!-- 右侧面板 -->
            <Border Grid.Column="2" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="1,0,0,0">
                <Grid RowDefinitions="Auto,*">
                    
                    <!-- 统计信息 -->
                    <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" 
                            BorderThickness="0,0,0,1" Padding="15">
                        <StackPanel Spacing="10">
                            <TextBlock Text="统计信息" FontWeight="Bold" FontSize="14"/>
                            <TextBlock Text="{Binding ClientListViewModel.StatisticsText}"/>
                            
                            <Separator Margin="0,10"/>
                            
                            <StackPanel Spacing="5">
                                <Button Content="全选" Command="{Binding ClientListViewModel.SelectAllCommand}" 
                                        HorizontalAlignment="Stretch"/>
                                <Button Content="清除选择" Command="{Binding ClientListViewModel.ClearSelectionCommand}" 
                                        HorizontalAlignment="Stretch"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 详细信息 -->
                    <ScrollViewer Grid.Row="1" Padding="15">
                        <StackPanel Spacing="10" IsVisible="{Binding ClientListViewModel.SelectedClient, 
                                                                     Converter={x:Static ObjectConverters.IsNotNull}}">
                            <TextBlock Text="客户端详情" FontWeight="Bold" FontSize="14"/>
                            
                            <StackPanel Spacing="5" DataContext="{Binding ClientListViewModel.SelectedClient}">
                                <TextBlock Text="{Binding DisplayName}" FontWeight="Bold" FontSize="16"/>
                                <Separator/>
                                
                                <Grid ColumnDefinitions="80,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto">
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="ID:" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ClientId}" TextWrapping="Wrap"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="主机名:" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Hostname}"/>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="IP地址:" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding IpAddress}"/>
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="操作系统:" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding OsInfo}" TextWrapping="Wrap"/>
                                    
                                    <TextBlock Grid.Row="4" Grid.Column="0" Text="CPU:" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding CpuInfo}" TextWrapping="Wrap"/>
                                    
                                    <TextBlock Grid.Row="5" Grid.Column="0" Text="内存:" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding Memory}"/>
                                    
                                    <TextBlock Grid.Row="6" Grid.Column="0" Text="显卡:" FontWeight="Bold"/>
                                    <TextBlock Grid.Row="6" Grid.Column="1" Text="{Binding GpuInfo}" TextWrapping="Wrap"/>
                                </Grid>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="10,5">
            <Grid ColumnDefinitions="*,Auto">
                <TextBlock Grid.Column="0" Text="{Binding ClientListViewModel.StatisticsText}" 
                           VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" Text="{Binding ConnectionStatus}" 
                           VerticalAlignment="Center" FontWeight="Bold"/>
            </Grid>
        </Border>
    </Grid>
</Window>
