<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:AvaloniaControlCenter.ViewModels"
        xmlns:controls="using:AvaloniaControlCenter.Controls"
        xmlns:views="using:AvaloniaControlCenter.Views"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1400" d:DesignHeight="900"
        x:Class="AvaloniaControlCenter.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Icon="/Assets/icon.ico"
        Title="{Binding Title}"
        Width="1400" Height="900"
        MinWidth="1200" MinHeight="700"
        WindowStartupLocation="CenterScreen">

    <Design.DataContext>
        <!-- This only sets the DataContext for the previewer in an IDE,
             to set the actual DataContext for runtime, set the DataContext property in code (look at App.axaml.cs) -->
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <Grid RowDefinitions="Auto,*,Auto">
        
        <!-- 顶部工具栏 -->
        <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
            <Grid ColumnDefinitions="Auto,*,Auto">
                
                <!-- 连接控制 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="10">
                    <TextBlock Text="服务器:" VerticalAlignment="Center"/>
                    <TextBox Text="{Binding ServerUrl}" Width="200" VerticalAlignment="Center"/>
                    <Button Content="连接" Command="{Binding ConnectCommand}" 
                            IsEnabled="{Binding !IsConnected}" Classes="primary"/>
                    <Button Content="断开" Command="{Binding DisconnectCommand}" 
                            IsEnabled="{Binding IsConnected}" Classes="danger"/>
                    <TextBlock Text="{Binding ConnectionStatus}" VerticalAlignment="Center"
                               Foreground="{Binding IsConnected, Converter={StaticResource BooleanToColorConverter}}"/>
                </StackPanel>

                <!-- 批量命令面板 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="8">
                    <TextBlock Text="批量命令:" VerticalAlignment="Center" FontWeight="SemiBold"/>
                    <TextBox Text="{Binding BatchCommandViewModel.CommandText}" Width="250"
                             VerticalAlignment="Center" Watermark="输入命令或选择模板..."/>
                    <ComboBox ItemsSource="{Binding BatchCommandViewModel.CommandTemplates}"
                              SelectedItem="{Binding BatchCommandViewModel.SelectedTemplate}"
                              Width="150" VerticalAlignment="Center">
                        <ComboBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Name}"/>
                            </DataTemplate>
                        </ComboBox.ItemTemplate>
                    </ComboBox>
                    <Button Content="📤 发送到选中"
                            Command="{Binding BatchCommandViewModel.ExecuteSelectedCommand}"
                            IsEnabled="{Binding IsConnected}" Classes="primary" Padding="8,4"/>
                    <Button Content="📡 广播到所有"
                            Command="{Binding BatchCommandViewModel.ExecuteAllOnlineCommand}"
                            IsEnabled="{Binding IsConnected}" Classes="secondary" Padding="8,4"/>
                    <Button Content="💾 保存模板"
                            Command="{Binding BatchCommandViewModel.SaveTemplateCommand}"
                            Padding="8,4"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" ColumnDefinitions="250,Auto,*,Auto,300">

            <!-- 左侧：分组管理面板 -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <views:GroupManagerView DataContext="{Binding GroupManagerViewModel}"/>
            </Border>

            <!-- 分隔符1 -->
            <GridSplitter Grid.Column="1" Width="5" Background="#E0E0E0"/>

            <!-- 中间：客户端列表 -->
            <Border Grid.Column="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <Grid RowDefinitions="Auto,*">
                    
                    <!-- 过滤控制 -->
                    <Border Grid.Row="0" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
                        <Grid ColumnDefinitions="*,Auto,Auto,Auto">
                            <TextBox Grid.Column="0" Text="{Binding ClientListViewModel.SearchText}" 
                                     Watermark="搜索客户端..." Margin="0,0,10,0"/>
                            <ComboBox Grid.Column="1" SelectedItem="{Binding ClientListViewModel.SelectedGroupName}"
                                      ItemsSource="{Binding ClientListViewModel.GroupNames}" 
                                      Width="120" Margin="0,0,10,0"/>
                            <CheckBox Grid.Column="2" Content="仅在线" 
                                      IsChecked="{Binding ClientListViewModel.ShowOnlineOnly}" 
                                      Margin="0,0,10,0"/>
                            <Button Grid.Column="3" Content="刷新" 
                                    Command="{Binding ClientListViewModel.RefreshCommand}"/>
                        </Grid>
                    </Border>

                    <!-- 虚拟化客户端列表 -->
                    <controls:VirtualizedClientList Grid.Row="1"
                                                     ItemsSource="{Binding ClientListViewModel.FilteredClients}">
                        <controls:VirtualizedClientList.ItemTemplate>
                            <DataTemplate DataType="vm:ClientItemViewModel">
                                <views:ClientItemView/>
                            </DataTemplate>
                        </controls:VirtualizedClientList.ItemTemplate>
                    </controls:VirtualizedClientList>
                </Grid>
            </Border>

            <!-- 分隔符2 -->
            <GridSplitter Grid.Column="3" Width="5" Background="#E0E0E0"/>

            <!-- 右侧面板 -->
            <Border Grid.Column="4" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="1,0,0,0">
                <Grid RowDefinitions="Auto,*">
                    
                    <!-- 批量操作面板 -->
                    <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0"
                            BorderThickness="0,0,0,1" Padding="15">
                        <StackPanel Spacing="12">
                            <!-- 统计信息 -->
                            <StackPanel Spacing="6">
                                <TextBlock Text="批量操作" FontWeight="Bold" FontSize="14"/>
                                <Grid ColumnDefinitions="*,*">
                                    <StackPanel Grid.Column="0" Spacing="2">
                                        <TextBlock Text="选中客户端" FontSize="11" Foreground="Gray"/>
                                        <TextBlock Text="{Binding BatchCommandViewModel.SelectedClientCount}"
                                                   FontWeight="Bold" FontSize="16" Foreground="#007BFF"/>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1" Spacing="2">
                                        <TextBlock Text="在线客户端" FontSize="11" Foreground="Gray"/>
                                        <TextBlock Text="{Binding BatchCommandViewModel.OnlineClientCount}"
                                                   FontWeight="Bold" FontSize="16" Foreground="#28A745"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>

                            <Separator/>

                            <!-- 选择操作 -->
                            <StackPanel Spacing="6">
                                <TextBlock Text="选择操作" FontWeight="SemiBold" FontSize="12"/>
                                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto">
                                    <Button Grid.Row="0" Grid.Column="0" Content="全选"
                                            Command="{Binding ClientListViewModel.SelectAllCommand}"
                                            HorizontalAlignment="Stretch" FontSize="11" Margin="0,0,3,2"/>
                                    <Button Grid.Row="0" Grid.Column="1" Content="清除选择"
                                            Command="{Binding ClientListViewModel.ClearSelectionCommand}"
                                            HorizontalAlignment="Stretch" FontSize="11" Margin="3,0,0,2"/>
                                    <Button Grid.Row="1" Grid.Column="0" Content="选择在线"
                                            HorizontalAlignment="Stretch" FontSize="11" Margin="0,2,3,0"/>
                                    <Button Grid.Row="1" Grid.Column="1" Content="反选"
                                            HorizontalAlignment="Stretch" FontSize="11" Margin="3,2,0,0"/>
                                </Grid>
                            </StackPanel>

                            <Separator/>

                            <!-- 执行状态 -->
                            <StackPanel Spacing="4">
                                <TextBlock Text="执行状态" FontWeight="SemiBold" FontSize="12"/>
                                <TextBlock Text="{Binding BatchCommandViewModel.ExecutionStatus}"
                                           FontSize="11" Foreground="Gray" TextWrapping="Wrap"/>
                                <ProgressBar IsIndeterminate="{Binding BatchCommandViewModel.IsExecuting}"
                                             Height="4" Margin="0,4"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 详细信息 -->
                    <ScrollViewer Grid.Row="1" Padding="15">
                        <StackPanel Spacing="15">

                            <!-- 客户端详情 -->
                            <StackPanel Spacing="8" IsVisible="{Binding ClientListViewModel.SelectedClient,
                                                                         Converter={x:Static ObjectConverters.IsNotNull}}">
                                <TextBlock Text="客户端详情" FontWeight="Bold" FontSize="13"/>
                                <Border Background="#F8F9FA" CornerRadius="4" Padding="10"
                                        DataContext="{Binding ClientListViewModel.SelectedClient}">
                                    <StackPanel Spacing="6">
                                        <TextBlock Text="{Binding DisplayName}" FontWeight="Bold" FontSize="14"/>
                                        <Grid ColumnDefinitions="60,*" RowDefinitions="Auto,Auto,Auto,Auto">
                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="IP:" FontSize="11" Foreground="Gray"/>
                                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding IpAddress}" FontSize="11"/>
                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="系统:" FontSize="11" Foreground="Gray"/>
                                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding OsInfo}" FontSize="11" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="CPU:" FontSize="11" Foreground="Gray"/>
                                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CpuInfo}" FontSize="11" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="内存:" FontSize="11" Foreground="Gray"/>
                                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Memory}" FontSize="11"/>
                                        </Grid>
                                    </StackPanel>
                                </Border>
                            </StackPanel>

                            <!-- 命令模板 -->
                            <StackPanel Spacing="8">
                                <TextBlock Text="命令模板" FontWeight="Bold" FontSize="13"/>
                                <ScrollViewer Height="200">
                                    <ItemsControl ItemsSource="{Binding BatchCommandViewModel.CommandTemplates}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1"
                                                        CornerRadius="3" Margin="0,2" Padding="8">
                                                    <Grid ColumnDefinitions="*,Auto">
                                                        <StackPanel Grid.Column="0" Spacing="2">
                                                            <TextBlock Text="{Binding Name}" FontWeight="SemiBold" FontSize="11"/>
                                                            <TextBlock Text="{Binding Command}" FontSize="10"
                                                                       Foreground="Gray" TextTrimming="CharacterEllipsis"/>
                                                            <TextBlock Text="{Binding Category}" FontSize="9"
                                                                       Foreground="#6C757D"/>
                                                        </StackPanel>
                                                        <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="2">
                                                            <Button Content="📋" FontSize="10" Padding="4,2"
                                                                    Command="{Binding $parent[ScrollViewer].((vm:MainWindowViewModel)DataContext).BatchCommandViewModel.LoadTemplateCommand}"
                                                                    CommandParameter="{Binding}"
                                                                    ToolTip.Tip="加载模板"/>
                                                            <Button Content="🗑️" FontSize="10" Padding="4,2"
                                                                    Command="{Binding $parent[ScrollViewer].((vm:MainWindowViewModel)DataContext).BatchCommandViewModel.DeleteTemplateCommand}"
                                                                    CommandParameter="{Binding}"
                                                                    ToolTip.Tip="删除模板"/>
                                                        </StackPanel>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>
                            </StackPanel>

                            <!-- 执行历史 -->
                            <StackPanel Spacing="8">
                                <TextBlock Text="执行历史" FontWeight="Bold" FontSize="13"/>
                                <ScrollViewer Height="150">
                                    <ItemsControl ItemsSource="{Binding BatchCommandViewModel.ExecutionHistory}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="White" BorderBrush="#E9ECEF" BorderThickness="1"
                                                        CornerRadius="3" Margin="0,2" Padding="6">
                                                    <StackPanel Spacing="2">
                                                        <Grid ColumnDefinitions="*,Auto">
                                                            <TextBlock Grid.Column="0" Text="{Binding Command}"
                                                                       FontWeight="SemiBold" FontSize="10"
                                                                       TextTrimming="CharacterEllipsis"/>
                                                            <TextBlock Grid.Column="1" Text="{Binding Status}"
                                                                       FontSize="9" Foreground="Gray"/>
                                                        </Grid>
                                                        <Grid ColumnDefinitions="*,Auto">
                                                            <TextBlock Grid.Column="0" Text="{Binding Target}"
                                                                       FontSize="9" Foreground="Gray"/>
                                                            <TextBlock Grid.Column="1" Text="{Binding Duration}"
                                                                       FontSize="9" Foreground="Gray"/>
                                                        </Grid>
                                                    </StackPanel>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="10,5">
            <Grid ColumnDefinitions="*,Auto">
                <TextBlock Grid.Column="0" Text="{Binding ClientListViewModel.StatisticsText}" 
                           VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" Text="{Binding ConnectionStatus}" 
                           VerticalAlignment="Center" FontWeight="Bold"/>
            </Grid>
        </Border>
    </Grid>
</Window>
