using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Controls.Templates;
using Avalonia.Data;
using AvaloniaControlCenter.ViewModels;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace AvaloniaControlCenter.Controls;

/// <summary>
/// 虚拟化客户端列表控件
/// 支持大量客户端的高性能显示
/// </summary>
public class VirtualizedClientList : TemplatedControl
{
    private ScrollViewer? _scrollViewer;
    private Panel? _itemsPanel;
    private readonly Dictionary<int, Control> _realizedItems = new();
    private readonly Queue<Control> _recycledItems = new();
    private double _itemHeight = 120; // 每个客户端项的高度
    private int _firstVisibleIndex = 0;
    private int _lastVisibleIndex = -1;

    /// <summary>
    /// 客户端列表数据源
    /// </summary>
    public static readonly StyledProperty<IEnumerable?> ItemsSourceProperty =
        AvaloniaProperty.Register<VirtualizedClientList, IEnumerable?>(nameof(ItemsSource));

    /// <summary>
    /// 客户端项模板
    /// </summary>
    public static readonly StyledProperty<IDataTemplate?> ItemTemplateProperty =
        AvaloniaProperty.Register<VirtualizedClientList, IDataTemplate?>(nameof(ItemTemplate));

    /// <summary>
    /// 选中项变更事件
    /// </summary>
    public event EventHandler<ClientItemViewModel>? SelectionChanged;

    /// <summary>
    /// 客户端列表数据源
    /// </summary>
    public IEnumerable? ItemsSource
    {
        get => GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }

    /// <summary>
    /// 客户端项模板
    /// </summary>
    public IDataTemplate? ItemTemplate
    {
        get => GetValue(ItemTemplateProperty);
        set => SetValue(ItemTemplateProperty, value);
    }

    /// <summary>
    /// 当前项数量
    /// </summary>
    public int ItemCount => (ItemsSource as IList)?.Count ?? 0;

    static VirtualizedClientList()
    {
        ItemsSourceProperty.Changed.AddClassHandler<VirtualizedClientList>((x, e) => x.OnItemsSourceChanged(e));
    }

    /// <summary>
    /// 应用模板
    /// </summary>
    protected override void OnApplyTemplate(TemplateAppliedEventArgs e)
    {
        base.OnApplyTemplate(e);

        _scrollViewer = e.NameScope.Find<ScrollViewer>("PART_ScrollViewer");
        _itemsPanel = e.NameScope.Find<Panel>("PART_ItemsPanel");

        if (_scrollViewer != null)
        {
            _scrollViewer.ScrollChanged += OnScrollChanged;
        }

        UpdateLayout();
    }

    /// <summary>
    /// 数据源变更处理
    /// </summary>
    private void OnItemsSourceChanged(AvaloniaPropertyChangedEventArgs e)
    {
        // 取消订阅旧数据源的变更通知
        if (e.OldValue is INotifyCollectionChanged oldCollection)
        {
            oldCollection.CollectionChanged -= OnCollectionChanged;
        }

        // 订阅新数据源的变更通知
        if (e.NewValue is INotifyCollectionChanged newCollection)
        {
            newCollection.CollectionChanged += OnCollectionChanged;
        }

        // 清理现有项
        ClearRealizedItems();
        
        // 更新布局
        UpdateLayout();
    }

    /// <summary>
    /// 集合变更处理
    /// </summary>
    private void OnCollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        switch (e.Action)
        {
            case NotifyCollectionChangedAction.Add:
            case NotifyCollectionChangedAction.Remove:
            case NotifyCollectionChangedAction.Replace:
                UpdateLayout();
                break;
            case NotifyCollectionChangedAction.Reset:
                ClearRealizedItems();
                UpdateLayout();
                break;
        }
    }

    /// <summary>
    /// 滚动变更处理
    /// </summary>
    private void OnScrollChanged(object? sender, ScrollChangedEventArgs e)
    {
        UpdateVisibleItems();
    }

    /// <summary>
    /// 更新虚拟化布局
    /// </summary>
    private new void UpdateLayout()
    {
        if (_itemsPanel == null || _scrollViewer == null)
            return;

        var itemCount = ItemCount;
        var totalHeight = itemCount * _itemHeight;

        // 设置面板高度
        _itemsPanel.Height = totalHeight;

        // 更新可见项
        UpdateVisibleItems();
    }

    /// <summary>
    /// 更新可见项
    /// </summary>
    private void UpdateVisibleItems()
    {
        if (_scrollViewer == null || _itemsPanel == null)
            return;

        var viewportHeight = _scrollViewer.Viewport.Height;
        var scrollOffset = _scrollViewer.Offset.Y;
        var itemCount = ItemCount;

        if (itemCount == 0)
        {
            ClearRealizedItems();
            return;
        }

        // 计算可见范围
        var firstVisible = Math.Max(0, (int)(scrollOffset / _itemHeight) - 1);
        var lastVisible = Math.Min(itemCount - 1, 
            (int)((scrollOffset + viewportHeight) / _itemHeight) + 1);

        // 回收不再可见的项
        var itemsToRecycle = _realizedItems.Where(kvp => 
            kvp.Key < firstVisible || kvp.Key > lastVisible).ToList();

        foreach (var item in itemsToRecycle)
        {
            _itemsPanel.Children.Remove(item.Value);
            _recycledItems.Enqueue(item.Value);
            _realizedItems.Remove(item.Key);
        }

        // 创建新的可见项
        for (int i = firstVisible; i <= lastVisible; i++)
        {
            if (!_realizedItems.ContainsKey(i))
            {
                var itemControl = CreateItemControl(i);
                if (itemControl != null)
                {
                    _realizedItems[i] = itemControl;
                    _itemsPanel.Children.Add(itemControl);
                    
                    // 设置位置
                    Canvas.SetTop(itemControl, i * _itemHeight);
                }
            }
        }

        _firstVisibleIndex = firstVisible;
        _lastVisibleIndex = lastVisible;
    }

    /// <summary>
    /// 创建项控件
    /// </summary>
    private Control? CreateItemControl(int index)
    {
        var items = ItemsSource as IList;
        if (items == null || index >= items.Count)
            return null;

        var dataItem = items[index];
        
        // 尝试从回收池获取控件
        Control itemControl;
        if (_recycledItems.Count > 0)
        {
            itemControl = _recycledItems.Dequeue();
        }
        else
        {
            // 创建新控件
            if (ItemTemplate != null)
            {
                itemControl = ItemTemplate.Build(dataItem) ?? new ContentControl();
            }
            else
            {
                itemControl = new ContentControl();
            }
        }

        // 设置数据上下文
        itemControl.DataContext = dataItem;

        // 订阅选择事件
        if (dataItem is ClientItemViewModel clientViewModel)
        {
            clientViewModel.Selected -= OnItemSelected;
            clientViewModel.Selected += OnItemSelected;
        }

        return itemControl;
    }

    /// <summary>
    /// 项选择事件处理
    /// </summary>
    private void OnItemSelected(object? sender, ClientItemViewModel e)
    {
        SelectionChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 清理已实现的项
    /// </summary>
    private void ClearRealizedItems()
    {
        if (_itemsPanel == null)
            return;

        foreach (var item in _realizedItems.Values)
        {
            _itemsPanel.Children.Remove(item);
            _recycledItems.Enqueue(item);
        }

        _realizedItems.Clear();
    }

    /// <summary>
    /// 滚动到指定项
    /// </summary>
    /// <param name="index">项索引</param>
    public void ScrollToItem(int index)
    {
        if (_scrollViewer == null)
            return;

        var targetOffset = index * _itemHeight;
        _scrollViewer.Offset = new Vector(_scrollViewer.Offset.X, targetOffset);
    }

    /// <summary>
    /// 刷新所有项
    /// </summary>
    public void RefreshItems()
    {
        ClearRealizedItems();
        UpdateLayout();
    }

    /// <summary>
    /// 获取可见项数量
    /// </summary>
    public int GetVisibleItemCount()
    {
        return _realizedItems.Count;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    protected override void OnDetachedFromVisualTree(VisualTreeAttachmentEventArgs e)
    {
        base.OnDetachedFromVisualTree(e);

        if (_scrollViewer != null)
        {
            _scrollViewer.ScrollChanged -= OnScrollChanged;
        }

        if (ItemsSource is INotifyCollectionChanged collection)
        {
            collection.CollectionChanged -= OnCollectionChanged;
        }

        ClearRealizedItems();
    }
}
