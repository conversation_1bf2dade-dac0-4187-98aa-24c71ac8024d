using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;

namespace AvaloniaControlCenter.Converters;

/// <summary>
/// 展开/折叠转换器
/// </summary>
public class ExpandCollapseConverter : IValueConverter
{
    public static readonly ExpandCollapseConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isExpanded)
        {
            return isExpanded ? "▼" : "▶";
        }
        return "▶";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 在线数量到颜色转换器
/// </summary>
public class OnlineCountToColorConverter : IValueConverter
{
    public static readonly OnlineCountToColorConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is int count)
        {
            return count switch
            {
                0 => Brushes.Gray,
                > 0 and <= 5 => Brushes.Orange,
                > 5 and <= 20 => Brushes.Blue,
                > 20 => Brushes.Green,
                _ => Brushes.Gray
            };
        }
        return Brushes.Gray;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 非默认分组转换器
/// </summary>
public class NotDefaultGroupConverter : IValueConverter
{
    public static readonly NotDefaultGroupConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is string groupName)
        {
            return !string.Equals(groupName, "默认分组", StringComparison.OrdinalIgnoreCase);
        }
        return true;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 分组名称验证转换器
/// </summary>
public class GroupNameValidationConverter : IValueConverter
{
    public static readonly GroupNameValidationConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is string groupName)
        {
            if (string.IsNullOrWhiteSpace(groupName))
                return false;
            
            if (groupName.Length > 50)
                return false;
                
            return true;
        }
        return false;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 客户端数量到文本转换器
/// </summary>
public class ClientCountToTextConverter : IValueConverter
{
    public static readonly ClientCountToTextConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is int count)
        {
            return count switch
            {
                0 => "无客户端",
                1 => "1个客户端",
                _ => $"{count}个客户端"
            };
        }
        return "无客户端";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 分组状态到图标转换器
/// </summary>
public class GroupStatusToIconConverter : IValueConverter
{
    public static readonly GroupStatusToIconConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is int onlineCount)
        {
            return onlineCount switch
            {
                0 => "😴",      // 无在线客户端
                > 0 and <= 5 => "😐",   // 少量在线
                > 5 and <= 20 => "😊",  // 中等在线
                > 20 => "🔥",           // 大量在线
                _ => "❓"
            };
        }
        return "❓";
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
