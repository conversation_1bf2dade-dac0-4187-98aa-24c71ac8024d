using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Data.Core.Plugins;
using Avalonia.Markup.Xaml;
using AvaloniaControlCenter.ViewModels;
using AvaloniaControlCenter.Views;
using Serilog;
using System;
using System.Collections.Generic;

namespace AvaloniaControlCenter;

public partial class App : Application
{
    private static readonly Dictionary<Type, object> _services = new();

    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        // 配置服务
        ConfigureServices();

        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            // Line below is needed to remove Avalonia data validation.
            // Without this line you will get duplicate validations from both Avalonia and CT
            BindingPlugins.DataValidators.RemoveAt(0);

            var mainWindowViewModel = GetService<MainWindowViewModel>();
            desktop.MainWindow = new MainWindow
            {
                DataContext = mainWindowViewModel,
            };
        }

        base.OnFrameworkInitializationCompleted();
    }

    private void ConfigureServices()
    {
        // 注册日志服务
        _services[typeof(ILogger)] = Log.Logger;

        // 注册Services
        _services[typeof(Services.WebSocketService)] = new Services.WebSocketService(Log.Logger);
        _services[typeof(Services.DispatcherQueue)] = new Services.DispatcherQueue(Log.Logger);
        _services[typeof(Services.ClientManager)] = new Services.ClientManager(
            Log.Logger,
            GetService<Services.WebSocketService>(),
            GetService<Services.DispatcherQueue>());

        // 注册ViewModels
        _services[typeof(ClientListViewModel)] = new ClientListViewModel(
            Log.Logger,
            GetService<Services.ClientManager>());
        _services[typeof(GroupManagerViewModel)] = new GroupManagerViewModel(
            Log.Logger,
            GetService<Services.ClientManager>());
        _services[typeof(BatchCommandViewModel)] = new BatchCommandViewModel(
            Log.Logger,
            GetService<Services.ClientManager>());
        _services[typeof(MainWindowViewModel)] = new MainWindowViewModel(
            Log.Logger,
            GetService<Services.WebSocketService>(),
            GetService<Services.ClientManager>(),
            GetService<ClientListViewModel>(),
            GetService<GroupManagerViewModel>(),
            GetService<BatchCommandViewModel>());
    }

    public static T GetService<T>()
    {
        if (_services.TryGetValue(typeof(T), out var service))
        {
            return (T)service;
        }
        throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered.");
    }
}
