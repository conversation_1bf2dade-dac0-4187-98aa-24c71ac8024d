is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = AvaloniaControlCenter
build_property.ProjectDir = F:\web\WebRTC远程客户管理后台\Avalonia+c#\Avalonia-control\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[F:/web/WebRTC远程客户管理后台/Avalonia+c\#/Avalonia-control/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[F:/web/WebRTC远程客户管理后台/Avalonia+c\#/Avalonia-control/Styles/VirtualizedClientListStyle.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[F:/web/WebRTC远程客户管理后台/Avalonia+c\#/Avalonia-control/Views/ClientItemView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[F:/web/WebRTC远程客户管理后台/Avalonia+c\#/Avalonia-control/Views/GroupManagerView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[F:/web/WebRTC远程客户管理后台/Avalonia+c\#/Avalonia-control/Views/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
