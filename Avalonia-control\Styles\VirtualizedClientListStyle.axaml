<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="using:AvaloniaControlCenter.Controls">

    <!-- 虚拟化客户端列表样式 -->
    <Style Selector="controls|VirtualizedClientList">
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <ScrollViewer Name="PART_ScrollViewer"
                                  HorizontalScrollBarVisibility="Disabled"
                                  VerticalScrollBarVisibility="Auto">
                        <Canvas Name="PART_ItemsPanel" 
                                Background="Transparent"
                                ClipToBounds="True"/>
                    </ScrollViewer>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>

    <!-- 客户端项样式 -->
    <Style Selector="Border.client-item-virtualized">
        <Setter Property="BorderBrush" Value="#E0E0E0"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="CornerRadius" Value="6"/>
        <Setter Property="MinHeight" Value="120"/>
    </Style>
    
    <Style Selector="Border.client-item-virtualized:pointerover">
        <Setter Property="Background" Value="#F8F9FA"/>
        <Setter Property="BorderBrush" Value="#DEE2E6"/>
    </Style>
    
    <Style Selector="Border.client-item-virtualized.selected">
        <Setter Property="Background" Value="#E3F2FD"/>
        <Setter Property="BorderBrush" Value="#2196F3"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- 状态指示器样式 -->
    <Style Selector="Ellipse.status-indicator">
        <Setter Property="Width" Value="16"/>
        <Setter Property="Height" Value="16"/>
        <Setter Property="StrokeThickness" Value="2"/>
        <Setter Property="Stroke" Value="White"/>
    </Style>

    <Style Selector="Ellipse.status-online">
        <Setter Property="Fill" Value="#4CAF50"/>
    </Style>

    <Style Selector="Ellipse.status-offline">
        <Setter Property="Fill" Value="#9E9E9E"/>
    </Style>

    <Style Selector="Ellipse.status-connecting">
        <Setter Property="Fill" Value="#FF9800"/>
    </Style>

    <Style Selector="Ellipse.status-disconnecting">
        <Setter Property="Fill" Value="#F44336"/>
    </Style>

    <!-- 状态标签样式 -->
    <Style Selector="Border.status-badge">
        <Setter Property="CornerRadius" Value="10"/>
        <Setter Property="Padding" Value="8,3"/>
        <Setter Property="Margin" Value="4,0"/>
    </Style>

    <Style Selector="Border.status-badge.online">
        <Setter Property="Background" Value="#4CAF50"/>
    </Style>

    <Style Selector="Border.status-badge.offline">
        <Setter Property="Background" Value="#9E9E9E"/>
    </Style>

    <Style Selector="Border.status-badge.connecting">
        <Setter Property="Background" Value="#FF9800"/>
    </Style>

    <Style Selector="Border.status-badge.disconnecting">
        <Setter Property="Background" Value="#F44336"/>
    </Style>

    <!-- 操作按钮样式 -->
    <Style Selector="Button.client-action">
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Padding" Value="10,5"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="MinWidth" Value="70"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>

    <Style Selector="Button.client-action.primary">
        <Setter Property="Background" Value="#2196F3"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#2196F3"/>
    </Style>

    <Style Selector="Button.client-action.primary:pointerover /template/ ContentPresenter">
        <Setter Property="Background" Value="#1976D2"/>
    </Style>

    <Style Selector="Button.client-action.secondary">
        <Setter Property="Background" Value="#6C757D"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#6C757D"/>
    </Style>

    <Style Selector="Button.client-action.secondary:pointerover /template/ ContentPresenter">
        <Setter Property="Background" Value="#5A6268"/>
    </Style>

    <Style Selector="Button.client-action.success">
        <Setter Property="Background" Value="#28A745"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#28A745"/>
    </Style>

    <Style Selector="Button.client-action.success:pointerover /template/ ContentPresenter">
        <Setter Property="Background" Value="#218838"/>
    </Style>

    <!-- 信息文本样式 -->
    <Style Selector="TextBlock.client-name">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="#212529"/>
    </Style>

    <Style Selector="TextBlock.client-info">
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Foreground" Value="#6C757D"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style Selector="TextBlock.client-label">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="#495057"/>
        <Setter Property="Margin" Value="0,0,4,0"/>
    </Style>

    <Style Selector="TextBlock.client-value">
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Foreground" Value="#212529"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <Style Selector="TextBlock.client-ip">
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Foreground" Value="#007BFF"/>
        <Setter Property="FontFamily" Value="Consolas,Monaco,monospace"/>
    </Style>

    <Style Selector="TextBlock.client-id">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="Foreground" Value="#6C757D"/>
        <Setter Property="FontFamily" Value="Consolas,Monaco,monospace"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <!-- 选择框样式 -->
    <Style Selector="CheckBox.client-selector">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 分组标题样式 -->
    <Style Selector="TextBlock.group-header">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Foreground" Value="#495057"/>
        <Setter Property="Margin" Value="8,12,8,4"/>
    </Style>

    <!-- 统计信息样式 -->
    <Style Selector="TextBlock.stats-text">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="#6C757D"/>
        <Setter Property="Margin" Value="4,0"/>
    </Style>

    <Style Selector="TextBlock.stats-number">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="#007BFF"/>
    </Style>

</Styles>
