using ReactiveUI;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AvaloniaControlCenter.ViewModels;

public class ViewModelBase : ReactiveObject, INotifyPropertyChanged
{
    /// <summary>
    /// 属性变更事件
    /// </summary>
    public new event PropertyChangedEventHandler? PropertyChanged;

    /// <summary>
    /// 设置属性值并触发属性变更通知
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="field">属性字段</param>
    /// <param name="value">新值</param>
    /// <param name="propertyName">属性名称</param>
    /// <returns>是否发生了变更</returns>
    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    /// <summary>
    /// 触发属性变更通知
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        this.RaisePropertyChanged(propertyName);
    }

    /// <summary>
    /// 触发多个属性变更通知
    /// </summary>
    /// <param name="propertyNames">属性名称数组</param>
    protected void OnPropertiesChanged(params string[] propertyNames)
    {
        foreach (var propertyName in propertyNames)
        {
            OnPropertyChanged(propertyName);
        }
    }

    /// <summary>
    /// 安全执行UI操作
    /// </summary>
    /// <param name="action">要执行的操作</param>
    protected void ExecuteOnUIThread(Action action)
    {
        try
        {
            if (Avalonia.Threading.Dispatcher.UIThread.CheckAccess())
            {
                action();
            }
            else
            {
                Avalonia.Threading.Dispatcher.UIThread.Post(action);
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常，避免崩溃
            System.Diagnostics.Debug.WriteLine($"UI操作执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 异步安全执行UI操作
    /// </summary>
    /// <param name="action">要执行的操作</param>
    protected async System.Threading.Tasks.Task ExecuteOnUIThreadAsync(Action action)
    {
        try
        {
            if (Avalonia.Threading.Dispatcher.UIThread.CheckAccess())
            {
                action();
            }
            else
            {
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(action);
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常，避免崩溃
            System.Diagnostics.Debug.WriteLine($"异步UI操作执行失败: {ex.Message}");
        }
    }
}
