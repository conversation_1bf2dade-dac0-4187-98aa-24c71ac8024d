python -m virtualenv venv
venv\scripts\activate.bat

放行端口
ufw allow 443
ufw allow 8000

# 安装依赖
sudo apt update
sudo apt install python3-pip
pip3 install fastapi uvicorn websockets aioredis
pip3 install -U fastapi uvicorn[standard] websockets redis python-multipart
# 安装Redis
sudo apt install redis-server

# 启动服务器
python3 server1.3.py

开始执行PyInstaller命令..
pip install PyInstaller
pip install --force-reinstall pyinstaller websockets backoff wmi pywin32


客户端
pip install websockets backoff
pip install wmi pywin32
pip install opencv-python
pip install mss
pip install httpx
pip install aiofiles
pip install tqdm
pip install pytest
pip install msgpack psutil

控制端
pip install asyncio
pip install websockets
pip install pillow
pip install opencv-python
pip install numpy
pip install requests

卸载
cd C:\ProgramData\RuntimeBroke
Servicests.exe stop
Servicests.exe remove
sc delete ProcessMonitorService

# 3. 安装新服务（自动启动）
cd C:\ProgramData\RuntimeBroke
Servicests.exe install
sc config Servicests start= auto
Servicests.exe start

强制结束指定进程
TASKKILL /F /IM cmd.exe /T
TASKKILL /F /FI "PID ge 1000" 



TASKKILL /F /IM msedge.exe   /T


***************
administrator
Aa445566