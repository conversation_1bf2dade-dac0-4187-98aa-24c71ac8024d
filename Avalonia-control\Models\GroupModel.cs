using System.Collections.ObjectModel;
using System.Linq;

namespace AvaloniaControlCenter.Models;

/// <summary>
/// 分组模型
/// </summary>
public class GroupModel
{
    /// <summary>
    /// 分组名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 分组描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否展开
    /// </summary>
    public bool IsExpanded { get; set; } = true;

    /// <summary>
    /// 分组中的客户端列表
    /// </summary>
    public ObservableCollection<ClientModel> Clients { get; set; } = new();

    /// <summary>
    /// 在线客户端数量
    /// </summary>
    public int OnlineCount => Clients.Count(c => c.Status == ClientStatus.Online);

    /// <summary>
    /// 总客户端数量
    /// </summary>
    public int TotalCount => Clients.Count;

    /// <summary>
    /// 分组显示文本
    /// </summary>
    public string DisplayText => $"{Name} ({OnlineCount}/{TotalCount})";

    /// <summary>
    /// 添加客户端到分组
    /// </summary>
    /// <param name="client">客户端</param>
    public void AddClient(ClientModel client)
    {
        if (!Clients.Contains(client))
        {
            client.GroupName = Name;
            Clients.Add(client);
        }
    }

    /// <summary>
    /// 从分组中移除客户端
    /// </summary>
    /// <param name="client">客户端</param>
    public void RemoveClient(ClientModel client)
    {
        Clients.Remove(client);
    }

    /// <summary>
    /// 根据客户端ID移除客户端
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    public void RemoveClient(string clientId)
    {
        var client = Clients.FirstOrDefault(c => c.ClientId == clientId);
        if (client != null)
        {
            Clients.Remove(client);
        }
    }

    /// <summary>
    /// 查找客户端
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>客户端模型</returns>
    public ClientModel? FindClient(string clientId)
    {
        return Clients.FirstOrDefault(c => c.ClientId == clientId);
    }

    /// <summary>
    /// 清空分组
    /// </summary>
    public void Clear()
    {
        Clients.Clear();
    }
}
