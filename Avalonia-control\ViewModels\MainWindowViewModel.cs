using AvaloniaControlCenter.Services;
using ReactiveUI;
using Serilog;
using System;
using System.Reactive;
using System.Threading.Tasks;

namespace AvaloniaControlCenter.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public class MainWindowViewModel : ViewModelBase
{
    private readonly ILogger _logger;
    private readonly WebSocketService _webSocketService;
    private readonly ClientManager _clientManager;
    private string _serverUrl = "ws://microsoft.com:8081";
    private string _connectionStatus = "未连接";
    private bool _isConnected = false;
    private string _commandText = string.Empty;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="webSocketService">WebSocket服务</param>
    /// <param name="clientManager">客户端管理器</param>
    /// <param name="clientListViewModel">客户端列表ViewModel</param>
    public MainWindowViewModel(
        ILogger logger, 
        WebSocketService webSocketService, 
        ClientManager clientManager,
        ClientListViewModel clientListViewModel)
    {
        _logger = logger;
        _webSocketService = webSocketService;
        _clientManager = clientManager;
        ClientListViewModel = clientListViewModel;

        // 创建命令
        ConnectCommand = ReactiveCommand.CreateFromTask(ConnectAsync);
        DisconnectCommand = ReactiveCommand.CreateFromTask(DisconnectAsync);
        SendCommandCommand = ReactiveCommand.CreateFromTask(SendCommandAsync);
        BroadcastCommandCommand = ReactiveCommand.CreateFromTask(BroadcastCommandAsync);
        ExitCommand = ReactiveCommand.Create(Exit);

        // 订阅WebSocket事件
        _webSocketService.ConnectionStateChanged += OnConnectionStateChanged;
        _webSocketService.ErrorOccurred += OnErrorOccurred;

        // 订阅客户端列表事件
        ClientListViewModel.ScreenShareRequested += OnScreenShareRequested;
        ClientListViewModel.FileManagerRequested += OnFileManagerRequested;
        ClientListViewModel.CmdRequested += OnCmdRequested;

        // 初始化
        UpdateConnectionStatus();
    }

    /// <summary>
    /// 窗口标题
    /// </summary>
    public string Title => "Avalonia 控制中心 - 高性能远程客户端管理";

    /// <summary>
    /// 服务器URL
    /// </summary>
    public string ServerUrl
    {
        get => _serverUrl;
        set => SetProperty(ref _serverUrl, value);
    }

    /// <summary>
    /// 连接状态文本
    /// </summary>
    public string ConnectionStatus
    {
        get => _connectionStatus;
        private set => SetProperty(ref _connectionStatus, value);
    }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected
    {
        get => _isConnected;
        private set => SetProperty(ref _isConnected, value);
    }

    /// <summary>
    /// 命令文本
    /// </summary>
    public string CommandText
    {
        get => _commandText;
        set => SetProperty(ref _commandText, value);
    }

    /// <summary>
    /// 客户端列表ViewModel
    /// </summary>
    public ClientListViewModel ClientListViewModel { get; }

    /// <summary>
    /// 连接命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> ConnectCommand { get; }

    /// <summary>
    /// 断开连接命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> DisconnectCommand { get; }

    /// <summary>
    /// 发送命令到选中客户端
    /// </summary>
    public ReactiveCommand<Unit, Unit> SendCommandCommand { get; }

    /// <summary>
    /// 广播命令到所有客户端
    /// </summary>
    public ReactiveCommand<Unit, Unit> BroadcastCommandCommand { get; }

    /// <summary>
    /// 退出命令
    /// </summary>
    public ReactiveCommand<Unit, Unit> ExitCommand { get; }

    /// <summary>
    /// 连接到服务器
    /// </summary>
    private async Task ConnectAsync()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(_serverUrl))
            {
                _logger.Warning("服务器URL不能为空");
                return;
            }

            _logger.Information("正在连接到服务器: {ServerUrl}", _serverUrl);
            await _webSocketService.ConnectAsync(_serverUrl);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "连接服务器失败");
            ConnectionStatus = $"连接失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 断开连接
    /// </summary>
    private async Task DisconnectAsync()
    {
        try
        {
            _logger.Information("正在断开连接");
            await _webSocketService.DisconnectAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "断开连接失败");
        }
    }

    /// <summary>
    /// 发送命令到选中客户端
    /// </summary>
    private async Task SendCommandAsync()
    {
        if (string.IsNullOrWhiteSpace(_commandText))
        {
            _logger.Warning("命令不能为空");
            return;
        }

        try
        {
            await _clientManager.SendCommandToSelectedAsync(_commandText);
            CommandText = string.Empty; // 清空命令文本
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "发送命令失败");
        }
    }

    /// <summary>
    /// 广播命令到所有客户端
    /// </summary>
    private async Task BroadcastCommandAsync()
    {
        if (string.IsNullOrWhiteSpace(_commandText))
        {
            _logger.Warning("命令不能为空");
            return;
        }

        try
        {
            await _clientManager.BroadcastCommandAsync(_commandText);
            CommandText = string.Empty; // 清空命令文本
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "广播命令失败");
        }
    }

    /// <summary>
    /// 退出应用程序
    /// </summary>
    private void Exit()
    {
        try
        {
            _logger.Information("正在退出应用程序");
            
            // 断开连接
            if (IsConnected)
            {
                _ = Task.Run(async () => await _webSocketService.DisconnectAsync());
            }

            // 退出应用程序
            Environment.Exit(0);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "退出应用程序时发生错误");
        }
    }

    /// <summary>
    /// 连接状态变更事件处理
    /// </summary>
    private void OnConnectionStateChanged(object? sender, WebSocketConnectionState state)
    {
        ExecuteOnUIThread(() =>
        {
            UpdateConnectionStatus();
        });
    }

    /// <summary>
    /// 错误事件处理
    /// </summary>
    private void OnErrorOccurred(object? sender, Exception ex)
    {
        ExecuteOnUIThread(() =>
        {
            ConnectionStatus = $"错误: {ex.Message}";
        });
    }

    /// <summary>
    /// 屏幕共享请求事件处理
    /// </summary>
    private void OnScreenShareRequested(object? sender, ClientItemViewModel client)
    {
        try
        {
            _logger.Information("请求屏幕共享: {ClientId}", client.ClientId);
            // TODO: 打开屏幕共享窗口
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "打开屏幕共享失败");
        }
    }

    /// <summary>
    /// 文件管理请求事件处理
    /// </summary>
    private void OnFileManagerRequested(object? sender, ClientItemViewModel client)
    {
        try
        {
            _logger.Information("请求文件管理: {ClientId}", client.ClientId);
            // TODO: 打开文件管理窗口
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "打开文件管理失败");
        }
    }

    /// <summary>
    /// CMD请求事件处理
    /// </summary>
    private void OnCmdRequested(object? sender, ClientItemViewModel client)
    {
        try
        {
            _logger.Information("请求CMD: {ClientId}", client.ClientId);
            // TODO: 打开CMD窗口
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "打开CMD失败");
        }
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    private void UpdateConnectionStatus()
    {
        var state = _webSocketService.ConnectionState;
        IsConnected = state == WebSocketConnectionState.Connected;
        
        ConnectionStatus = state switch
        {
            WebSocketConnectionState.Connected => "已连接",
            WebSocketConnectionState.Connecting => "连接中...",
            WebSocketConnectionState.Disconnecting => "断开中...",
            WebSocketConnectionState.Disconnected => "未连接",
            _ => "未知状态"
        };
    }
}
