using System;
using System.Text.Json.Serialization;

namespace AvaloniaControlCenter.Models;

/// <summary>
/// 客户端状态枚举
/// </summary>
public enum ClientStatus
{
    Online,
    Offline,
    Connecting,
    Disconnecting
}

/// <summary>
/// 客户端模型
/// </summary>
public class ClientModel
{
    /// <summary>
    /// 客户端唯一标识
    /// </summary>
    [JsonPropertyName("client_id")]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 设备名称/主机名
    /// </summary>
    [JsonPropertyName("hostname")]
    public string Hostname { get; set; } = string.Empty;

    /// <summary>
    /// 备注名称
    /// </summary>
    [JsonPropertyName("remark")]
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// CPU信息
    /// </summary>
    [JsonPropertyName("cpu_info")]
    public string CpuInfo { get; set; } = string.Empty;

    /// <summary>
    /// 内存信息
    /// </summary>
    [JsonPropertyName("memory")]
    public string Memory { get; set; } = string.Empty;

    /// <summary>
    /// 显卡信息
    /// </summary>
    [JsonPropertyName("gpu_info")]
    public string GpuInfo { get; set; } = string.Empty;

    /// <summary>
    /// 操作系统信息
    /// </summary>
    [JsonPropertyName("os_info")]
    public string OsInfo { get; set; } = string.Empty;

    /// <summary>
    /// IP地址
    /// </summary>
    [JsonPropertyName("ip_address")]
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// IP归属地
    /// </summary>
    public string IpLocation { get; set; } = string.Empty;

    /// <summary>
    /// Python版本
    /// </summary>
    [JsonPropertyName("python_version")]
    public string PythonVersion { get; set; } = string.Empty;

    /// <summary>
    /// 注册时间
    /// </summary>
    [JsonPropertyName("registration_time")]
    public string RegistrationTime { get; set; } = string.Empty;

    /// <summary>
    /// 最后在线时间
    /// </summary>
    [JsonPropertyName("last_seen")]
    public string LastSeen { get; set; } = string.Empty;

    /// <summary>
    /// 客户端状态
    /// </summary>
    [JsonPropertyName("status")]
    public string StatusString { get; set; } = "offline";

    /// <summary>
    /// 客户端状态（枚举）
    /// </summary>
    [JsonIgnore]
    public ClientStatus Status
    {
        get => StatusString?.ToLower() switch
        {
            "online" => ClientStatus.Online,
            "offline" => ClientStatus.Offline,
            "connecting" => ClientStatus.Connecting,
            "disconnecting" => ClientStatus.Disconnecting,
            _ => ClientStatus.Offline
        };
        set => StatusString = value.ToString().ToLower();
    }

    /// <summary>
    /// 所属分组
    /// </summary>
    public string GroupName { get; set; } = "默认分组";

    /// <summary>
    /// 是否被选中
    /// </summary>
    [JsonIgnore]
    public bool IsSelected { get; set; }

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    [JsonIgnore]
    public DateTime LastHeartbeat { get; set; } = DateTime.Now;

    /// <summary>
    /// 获取显示名称（优先显示备注，否则显示主机名）
    /// </summary>
    [JsonIgnore]
    public string DisplayName => !string.IsNullOrWhiteSpace(Remark) ? Remark : Hostname;

    /// <summary>
    /// 获取状态显示文本
    /// </summary>
    [JsonIgnore]
    public string StatusText => Status switch
    {
        ClientStatus.Online => "在线",
        ClientStatus.Offline => "离线",
        ClientStatus.Connecting => "连接中",
        ClientStatus.Disconnecting => "断开中",
        _ => "未知"
    };

    /// <summary>
    /// 获取最后在线时间的显示文本
    /// </summary>
    [JsonIgnore]
    public string LastSeenDisplay
    {
        get
        {
            if (string.IsNullOrWhiteSpace(LastSeen))
                return "未知";

            if (DateTime.TryParse(LastSeen, out var lastSeenTime))
            {
                var timeSpan = DateTime.Now - lastSeenTime;
                if (timeSpan.TotalMinutes < 1)
                    return "刚刚";
                if (timeSpan.TotalHours < 1)
                    return $"{(int)timeSpan.TotalMinutes}分钟前";
                if (timeSpan.TotalDays < 1)
                    return $"{(int)timeSpan.TotalHours}小时前";
                return $"{(int)timeSpan.TotalDays}天前";
            }

            return LastSeen;
        }
    }

    /// <summary>
    /// 克隆客户端模型
    /// </summary>
    /// <returns>克隆的客户端模型</returns>
    public ClientModel Clone()
    {
        return new ClientModel
        {
            ClientId = ClientId,
            Hostname = Hostname,
            Remark = Remark,
            CpuInfo = CpuInfo,
            Memory = Memory,
            GpuInfo = GpuInfo,
            OsInfo = OsInfo,
            IpAddress = IpAddress,
            IpLocation = IpLocation,
            PythonVersion = PythonVersion,
            RegistrationTime = RegistrationTime,
            LastSeen = LastSeen,
            StatusString = StatusString,
            GroupName = GroupName,
            IsSelected = IsSelected,
            LastHeartbeat = LastHeartbeat
        };
    }
}
