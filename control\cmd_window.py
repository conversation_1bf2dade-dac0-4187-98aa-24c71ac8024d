import tkinter as tk
from tkinter import ttk, scrolledtext
import json
import asyncio
import logging

logger = logging.getLogger(__name__)

class CmdWindow:
    def __init__(self, client_id, client_hostname, send_command_callback):
        self.window = tk.Toplevel()
        self.window.title(f"CMD命令 - {client_hostname}")
        self.window.geometry("860x600")
        self.window.iconbitmap("icon.ico")
        # 界面位置置于主窗口中央
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() - self.window.winfo_reqwidth()) / 2
        y = (self.window.winfo_screenheight() - self.window.winfo_reqheight()) / 2
        self.window.geometry(f"+{int(x)}+{int(y)}")

        # 保存client_id用于清理
        self.client_id = client_id
        
        # 添加对控制端的引用以便清理
        self.control_gui = None  # 将在创建时设置 

        # 创建输出文本区域
        self.output_text = scrolledtext.ScrolledText(
            self.window,
            wrap=tk.WORD,
            background='black',
            foreground='white',
            font=('Consolas', 10)
        )
        self.output_text.pack(expand=True, fill='both', padx=5, pady=5)
        
        # 创建常用命令按钮框架
        self.button_frame = ttk.Frame(self.window)
        self.button_frame.pack(fill='x', padx=5)
        
        # 定义常用命令按钮
        common_commands = [
            ("ipconfig", "IP配置"),
            ("systeminfo", "系统信息"),
            ("tasklist", "任务列表"),
            ("taskkill /PID ", "结束进程"),
            ("netstat -ano", "网络连接"),
            ("wmic logicaldisk get name", "磁盘信息"),
            ("whoami", "当前用户"),
            ("powercfg -change -standby-timeout-ac 0", "禁止休眠"),
            ("shutdown /s /t 0", "关机"),
            ("shutdown /r /t 0 /f", "重启")
        ]
        
        # 创建按钮组
        for command, text in common_commands:
            btn = ttk.Button(
                self.button_frame,
                text=text,
                command=lambda cmd=command: self.insert_command(cmd),
                width=9  # 设置按钮宽度为80
            )
            btn.pack(side='left', padx=2, pady=2)
            
        # 添加清屏按钮（靠右）
        clear_btn = ttk.Button(
            self.button_frame,
            text="清屏",
            command=self.clear_screen
        )
        clear_btn.pack(side='right', padx=2, pady=2)
        
        # 创建输入框
        self.input_frame = ttk.Frame(self.window)
        self.input_frame.pack(fill='x', padx=5, pady=5)
        
        self.prompt_label = ttk.Label(self.input_frame, text=">")
        self.prompt_label.pack(side='left')
        
        self.command_entry = ttk.Entry(self.input_frame)
        self.command_entry.pack(side='left', fill='x', expand=True)
        
        # 绑定回车键事件
        self.command_entry.bind('<Return>', self.send_command)
        
        # 存储回调函数和客户端信息
        self.send_command_callback = send_command_callback
        self.client_id = client_id
        
        # 添加窗口关闭事件处理
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 设置焦点到输入框
        self.command_entry.focus()
        
        # 显示欢迎信息
        self.append_output("Microsoft Windows [版本 10.0.19045.3803]\n")
        self.append_output("(c) Microsoft Corporation。保留所有权利。\n\n")
        self.append_output(f"连接到客户端: {client_hostname}\n")
        self.append_output("输入命令并按回车执行...\n\n")
        
    def insert_command(self, command):
        """将命令插入到输入框"""
        self.command_entry.delete(0, tk.END)
        self.command_entry.insert(0, command)
        self.command_entry.focus()
        
    def clear_screen(self):
        """清空输出区域"""
        self.output_text.configure(state='normal')
        self.output_text.delete(1.0, tk.END)
        self.output_text.configure(state='disabled')
        # 重新显示欢迎信息
        self.append_output("Microsoft Windows [版本 10.0.19045.3803]\n")
        self.append_output("(c) Microsoft Corporation。保留所有权利。\n\n")
        self.append_output("输入命令并按回车执行...\n\n")
        
    def send_command(self, event=None):
        """发送命令"""
        command = self.command_entry.get().strip()
        if command:
            # 显示输入的命令
            self.append_output(f">{command}\n")
            # 清空输入框
            self.command_entry.delete(0, tk.END)
            # 如果命令不是以 "cmd " 开头，则添加前缀
            if not command.lower().startswith('cmd '):
                # 特殊命令不添加 cmd 前缀
                special_commands = ['hi', 'exit', 'quit', 'cls']
                if command.lower() not in special_commands:
                    command = f"cmd {command}"
            # 发送命令
            if self.send_command_callback:
                self.send_command_callback(command, self.client_id)
    
    def append_output(self, text):
        """添加输出文本到CMD窗口"""
        def _append():
            try:
                self.output_text.configure(state='normal')
                self.output_text.insert(tk.END, text)
                self.output_text.configure(state='disabled')
                # 自动滚动到底部
                self.output_text.see(tk.END)
                logger.info(f"已将文本添加到CMD窗口: {text}")
            except Exception as e:
                logger.error(f"添加文本到CMD窗口时出错: {str(e)}")
                logger.exception("详细错误信息:")
        
        # 在主线程中执行UI更新
        if self.window:
            self.window.after(0, _append)
    
    def on_closing(self):
        """窗口关闭处理"""
        try:
            # 从控制端的cmd_windows字典中移除此窗口
            if self.control_gui and hasattr(self.control_gui, 'cmd_windows'):
                if self.client_id in self.control_gui.cmd_windows:
                    del self.control_gui.cmd_windows[self.client_id]
        except Exception as e:
            logger.error(f"清理CMD窗口时出错: {e}")
        finally:
            self.window.destroy()
