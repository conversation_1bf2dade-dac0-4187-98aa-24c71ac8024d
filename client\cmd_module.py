import asyncio
import subprocess
import logging
import os
import win32api
import win32con
from typing import Optional, Dict
from asyncio.subprocess import Process

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CmdModule:
    def __init__(self):
        self.sessions: Dict[str, Process] = {}
        self.output_queues: Dict[str, asyncio.Queue] = {}
        
    async def create_terminal_session(self) -> Optional[subprocess.Popen]:
        """创建一个新的PowerShell会话"""
        try:
            # 使用 PowerShell，并设置为隐藏窗口
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # 获取PowerShell的完整路径
            powershell_path = os.path.join(os.environ["WINDIR"], "System32", "WindowsPowerShell", "v1.0", "powershell.exe")
            logger.info(f"使用PowerShell路径: {powershell_path}")
            
            if not os.path.exists(powershell_path):
                logger.error(f"PowerShell路径不存在: {powershell_path}")
                return None
            
            # 检查当前工作目录
            current_dir = os.getcwd()
            logger.info(f"当前工作目录: {current_dir}")
            
            # 检查PowerShell的访问权限
            try:
                win32api.GetFileAttributes(powershell_path)
                logger.info("成功获取PowerShell文件属性")
            except Exception as e:
                logger.error(f"无法访问PowerShell: {str(e)}")
                return None
            
            try:
                # 创建进程
                logger.info("开始创建PowerShell进程...")
                process = subprocess.Popen(
                    [
                        powershell_path,
                        "-NoLogo",  # 不显示Logo
                        "-NoExit",  # 执行后不退出
                        "-NonInteractive",  # 非交互模式
                        "-OutputFormat", "Text",  # 文本输出格式
                        "-Command", "-"  # 从标准输入读取命令
                    ],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    startupinfo=startupinfo,
                    cwd=current_dir,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    text=True,  # 使用文本模式
                    encoding='utf-8'  # 使用UTF-8编码
                )
                
                logger.info("成功创建PowerShell进程")
                
                # 等待一小段时间确保进程启动
                await asyncio.sleep(0.1)
                
                if process.poll() is not None:
                    logger.error(f"进程已退出，返回码: {process.returncode}")
                    return None
                
                # 设置一些PowerShell的初始配置
                init_commands = [
                    "$OutputEncoding = [System.Text.Encoding]::UTF8",  # 设置输出编码
                    "$PSDefaultParameterValues['*:Encoding'] = 'utf8'",  # 设置默认编码
                    "Clear-Host"  # 清屏
                ]
                
                for cmd in init_commands:
                    process.stdin.write(f"{cmd}\n")
                process.stdin.flush()
                
                return process
                
            except subprocess.SubprocessError as e:
                logger.error(f"创建子进程失败: {str(e)}")
                return None
            except WindowsError as e:
                logger.error(f"Windows错误: {str(e)}, 错误码: {e.winerror}")
                return None
            except Exception as e:
                logger.error(f"创建进程时发生未知错误: {str(e)}, 类型: {type(e)}")
                import traceback
                logger.error(f"错误堆栈: {traceback.format_exc()}")
                return None
            
        except Exception as e:
            logger.error(f"创建PowerShell会话失败: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None

    async def execute_command(self, process: subprocess.Popen, command: str) -> str:
        """执行命令并返回输出"""
        try:
            if not process or process.stdin is None or process.stdout is None:
                logger.error("无效的进程或进程管道")
                return "错误: 无效的进程"

            if process.poll() is not None:
                logger.error(f"进程已退出，返回码: {process.returncode}")
                return "错误: 进程已退出"

            # 处理 cd 命令
            if command.strip().lower().startswith('cd '):
                path = command.strip()[3:].strip().strip('"').strip("'")
                if path:
                    if not os.path.isabs(path):
                        path = os.path.join(os.getcwd(), path)
                    path = os.path.normpath(path)
                    
                    if os.path.exists(path):
                        # 使用PowerShell的Set-Location命令
                        cd_command = f'Set-Location -Path "{path}"\n'
                        try:
                            process.stdin.write(cd_command)
                            process.stdin.flush()
                            os.chdir(path)
                            return f"已切换到目录: {path}"
                        except Exception as e:
                            logger.error(f"执行cd命令失败: {str(e)}")
                            return f"切换目录失败: {str(e)}"
                    else:
                        return "错误: 指定的路径不存在。"

            # 执行命令
            command = command.strip()
            try:
                # 创建一个新的进程执行命令
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                
                # 如果是dir命令，使用PowerShell的Get-ChildItem
                if command.lower().startswith('dir'):
                    ps_command = (
                        "$OutputEncoding = [System.Text.Encoding]::UTF8; "
                        "$items = Get-ChildItem -Force; "
                        "Write-Output ('驱动器 ' + (Get-Location).Drive.Name + ': 中的目录'); "
                        "Write-Output ('目录: ' + (Get-Location).Path); "
                        "Write-Output ''; "
                        "$dirs = $items | Where-Object { $_.PSIsContainer }; "
                        "$files = $items | Where-Object { !$_.PSIsContainer }; "
                        "$totalSize = ($files | Measure-Object -Property Length -Sum).Sum; "
                        "$dirs | ForEach-Object { $_.LastWriteTime.ToString('yyyy/MM/dd  HH:mm') + '    <DIR>          ' + $_.Name }; "
                        "$files | ForEach-Object { $_.LastWriteTime.ToString('yyyy/MM/dd  HH:mm') + '  ' + $_.Length.ToString('#,0').PadLeft(14) + ' ' + $_.Name }; "
                        "Write-Output (''); "
                        "Write-Output (($files | Measure-Object).Count.ToString() + ' 个文件  ' + $totalSize.ToString('#,0') + ' 字节'); "
                        "Write-Output (($dirs | Measure-Object).Count.ToString() + ' 个目录  ' + (Get-PSDrive -Name (Get-Location).Drive.Name).Free.ToString('#,0') + ' 可用字节')"
                    )
                    cmd_process = subprocess.Popen(
                        [
                            os.path.join(os.environ["WINDIR"], "System32", "WindowsPowerShell", "v1.0", "powershell.exe"),
                            "-NoLogo",
                            "-NonInteractive",
                            "-Command",
                            ps_command
                        ],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        startupinfo=startupinfo,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        cwd=os.getcwd()
                    )
                else:
                    # 对于其他命令，直接使用CMD执行
                    cmd_process = subprocess.Popen(
                        f"cmd.exe /c chcp 936 > nul && {command}",
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        startupinfo=startupinfo,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        cwd=os.getcwd()
                    )
                
                # 使用communicate获取输出
                stdout_bytes, stderr_bytes = cmd_process.communicate(timeout=10)
                
                # 处理输出
                output = []
                if stdout_bytes:
                    try:
                        # 使用GBK解码
                        stdout_text = stdout_bytes.decode('gbk', errors='replace')
                        for line in stdout_text.splitlines():
                            if line.strip():
                                output.append(line.rstrip())
                    except Exception as e:
                        logger.error(f"解码stdout失败: {str(e)}")
                
                if stderr_bytes:
                    try:
                        # 使用GBK解码
                        stderr_text = stderr_bytes.decode('gbk', errors='replace')
                        for line in stderr_text.splitlines():
                            if line.strip():
                                output.append(f"错误: {line.strip()}")
                    except Exception as e:
                        logger.error(f"解码stderr失败: {str(e)}")
                
                result = '\n'.join(output)
                if not result:
                    result = "命令执行完成，无输出"
                return result
                
            except subprocess.TimeoutExpired:
                logger.error("命令执行超时")
                return "错误: 命令执行超时"
            except Exception as e:
                logger.error(f"执行命令失败: {str(e)}")
                return f"执行命令失败: {str(e)}"
            
        except Exception as e:
            logger.error(f"执行命令失败: {e}")
            return f"执行命令时出错: {str(e)}"

    async def handle_cmd_command(self, command: str) -> str:
        """处理PowerShell命令"""
        try:
            # 如果没有活动的进程，创建一个新的
            if not hasattr(self, 'process') or not self.process or self.process.returncode is not None:
                logger.info("创建新的PowerShell会话")
                self.process = await self.create_terminal_session()
                if not self.process:
                    return "创建PowerShell会话失败"
            
            # 执行命令并返回结果
            result = await self.execute_command(self.process, command)
            return result
            
        except Exception as e:
            logger.error(f"处理PowerShell命令时出错: {str(e)}")
            return f"执行命令失败: {str(e)}"