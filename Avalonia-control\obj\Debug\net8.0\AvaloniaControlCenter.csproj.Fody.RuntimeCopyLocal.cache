C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Base.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Controls.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.DesignerSupport.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Dialogs.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Markup.Xaml.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Markup.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Metal.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.MicroCom.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.OpenGL.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.colorpicker\11.0.10\lib\net6.0\Avalonia.Controls.ColorPicker.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.datagrid\11.0.10\lib\net6.0\Avalonia.Controls.DataGrid.dll
C:\Users\<USER>\.nuget\packages\avalonia.desktop\11.0.10\lib\net6.0\Avalonia.Desktop.dll
C:\Users\<USER>\.nuget\packages\avalonia.diagnostics\11.0.10\lib\net6.0\Avalonia.Diagnostics.dll
C:\Users\<USER>\.nuget\packages\avalonia.fonts.inter\11.0.10\lib\net6.0\Avalonia.Fonts.Inter.dll
C:\Users\<USER>\.nuget\packages\avalonia.freedesktop\11.0.10\lib\net6.0\Avalonia.FreeDesktop.dll
C:\Users\<USER>\.nuget\packages\avalonia.native\11.0.10\lib\net6.0\Avalonia.Native.dll
C:\Users\<USER>\.nuget\packages\avalonia.reactiveui\11.0.10\lib\net6.0\Avalonia.ReactiveUI.dll
C:\Users\<USER>\.nuget\packages\avalonia.remote.protocol\11.0.10\lib\net6.0\Avalonia.Remote.Protocol.dll
C:\Users\<USER>\.nuget\packages\avalonia.skia\11.0.10\lib\net6.0\Avalonia.Skia.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.fluent\11.0.10\lib\net6.0\Avalonia.Themes.Fluent.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.simple\11.0.10\lib\net6.0\Avalonia.Themes.Simple.dll
C:\Users\<USER>\.nuget\packages\avalonia.win32\11.0.10\lib\net6.0\Avalonia.Win32.dll
C:\Users\<USER>\.nuget\packages\avalonia.x11\11.0.10\lib\net6.0\Avalonia.X11.dll
C:\Users\<USER>\.nuget\packages\dynamicdata\8.3.27\lib\net8.0\DynamicData.dll
C:\Users\<USER>\.nuget\packages\harfbuzzsharp\7.3.0\lib\net6.0\HarfBuzzSharp.dll
C:\Users\<USER>\.nuget\packages\microcom.runtime\0.11.0\lib\net5.0\MicroCom.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\.nuget\packages\microsoft.csharp\4.3.0\lib\netstandard1.3\Microsoft.CSharp.dll
C:\Users\<USER>\.nuget\packages\reactiveui\19.5.41\lib\net8.0\ReactiveUI.dll
C:\Users\<USER>\.nuget\packages\reactiveui.fody\19.5.41\lib\net8.0\ReactiveUI.Fody.Helpers.dll
C:\Users\<USER>\.nuget\packages\serilog\3.1.1\lib\net7.0\Serilog.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.console\5.0.1\lib\net7.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.file\5.0.0\lib\net5.0\Serilog.Sinks.File.dll
C:\Users\<USER>\.nuget\packages\skiasharp\2.88.7\lib\net6.0\SkiaSharp.dll
C:\Users\<USER>\.nuget\packages\splat\14.8.12\lib\net8.0\Splat.dll
C:\Users\<USER>\.nuget\packages\system.collections.immutable\5.0.0\lib\netstandard2.0\System.Collections.Immutable.dll
C:\Users\<USER>\.nuget\packages\system.componentmodel.annotations\5.0.0\lib\netstandard2.1\System.ComponentModel.Annotations.dll
C:\Users\<USER>\.nuget\packages\system.dynamic.runtime\4.3.0\lib\netstandard1.3\System.Dynamic.Runtime.dll
C:\Users\<USER>\.nuget\packages\system.io.pipelines\6.0.0\lib\net6.0\System.IO.Pipelines.dll
C:\Users\<USER>\.nuget\packages\system.linq\4.3.0\lib\netstandard1.6\System.Linq.dll
C:\Users\<USER>\.nuget\packages\system.linq.expressions\4.3.0\lib\netstandard1.6\System.Linq.Expressions.dll
C:\Users\<USER>\.nuget\packages\system.objectmodel\4.3.0\lib\netstandard1.3\System.ObjectModel.dll
C:\Users\<USER>\.nuget\packages\system.reactive\6.0.0\lib\net6.0\System.Reactive.dll
C:\Users\<USER>\.nuget\packages\system.reflection.emit\4.3.0\lib\netstandard1.3\System.Reflection.Emit.dll
C:\Users\<USER>\.nuget\packages\system.reflection.emit.ilgeneration\4.3.0\lib\netstandard1.3\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\.nuget\packages\system.reflection.emit.lightweight\4.3.0\lib\netstandard1.3\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\.nuget\packages\system.reflection.metadata\5.0.0\lib\netstandard2.0\System.Reflection.Metadata.dll
C:\Users\<USER>\.nuget\packages\system.reflection.typeextensions\4.3.0\lib\netstandard1.5\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\.nuget\packages\system.runtime.compilerservices.unsafe\4.7.1\lib\netcoreapp2.0\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\.nuget\packages\system.text.encoding.codepages\4.5.1\lib\netstandard2.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\.nuget\packages\system.text.encodings.web\8.0.0\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\.nuget\packages\system.text.json\8.0.4\lib\net8.0\System.Text.Json.dll
C:\Users\<USER>\.nuget\packages\system.threading\4.3.0\lib\netstandard1.3\System.Threading.dll
C:\Users\<USER>\.nuget\packages\system.threading.channels\8.0.0\lib\net8.0\System.Threading.Channels.dll
C:\Users\<USER>\.nuget\packages\tmds.dbus.protocol\0.15.0\lib\net6.0\Tmds.DBus.Protocol.dll
