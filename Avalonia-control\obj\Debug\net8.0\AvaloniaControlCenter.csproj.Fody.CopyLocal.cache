C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Base.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Controls.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.DesignerSupport.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Dialogs.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Markup.Xaml.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Markup.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.Metal.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.MicroCom.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.OpenGL.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\lib\net6.0\Avalonia.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.colorpicker\11.0.10\lib\net6.0\Avalonia.Controls.ColorPicker.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.datagrid\11.0.10\lib\net6.0\Avalonia.Controls.DataGrid.dll
C:\Users\<USER>\.nuget\packages\avalonia.desktop\11.0.10\lib\net6.0\Avalonia.Desktop.dll
C:\Users\<USER>\.nuget\packages\avalonia.diagnostics\11.0.10\lib\net6.0\Avalonia.Diagnostics.dll
C:\Users\<USER>\.nuget\packages\avalonia.fonts.inter\11.0.10\lib\net6.0\Avalonia.Fonts.Inter.dll
C:\Users\<USER>\.nuget\packages\avalonia.freedesktop\11.0.10\lib\net6.0\Avalonia.FreeDesktop.dll
C:\Users\<USER>\.nuget\packages\avalonia.native\11.0.10\lib\net6.0\Avalonia.Native.dll
C:\Users\<USER>\.nuget\packages\avalonia.reactiveui\11.0.10\lib\net6.0\Avalonia.ReactiveUI.dll
C:\Users\<USER>\.nuget\packages\avalonia.remote.protocol\11.0.10\lib\net6.0\Avalonia.Remote.Protocol.dll
C:\Users\<USER>\.nuget\packages\avalonia.skia\11.0.10\lib\net6.0\Avalonia.Skia.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.fluent\11.0.10\lib\net6.0\Avalonia.Themes.Fluent.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.simple\11.0.10\lib\net6.0\Avalonia.Themes.Simple.dll
C:\Users\<USER>\.nuget\packages\avalonia.win32\11.0.10\lib\net6.0\Avalonia.Win32.dll
C:\Users\<USER>\.nuget\packages\avalonia.x11\11.0.10\lib\net6.0\Avalonia.X11.dll
C:\Users\<USER>\.nuget\packages\dynamicdata\8.3.27\lib\net8.0\DynamicData.dll
C:\Users\<USER>\.nuget\packages\harfbuzzsharp\7.3.0\lib\net6.0\HarfBuzzSharp.dll
C:\Users\<USER>\.nuget\packages\microcom.runtime\0.11.0\lib\net5.0\MicroCom.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\.nuget\packages\reactiveui\19.5.41\lib\net8.0\ReactiveUI.dll
C:\Users\<USER>\.nuget\packages\reactiveui.fody\19.5.41\lib\net8.0\ReactiveUI.Fody.Helpers.dll
C:\Users\<USER>\.nuget\packages\serilog\3.1.1\lib\net7.0\Serilog.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.console\5.0.1\lib\net7.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.file\5.0.0\lib\net5.0\Serilog.Sinks.File.dll
C:\Users\<USER>\.nuget\packages\skiasharp\2.88.7\lib\net6.0\SkiaSharp.dll
C:\Users\<USER>\.nuget\packages\splat\14.8.12\lib\net8.0\Splat.dll
C:\Users\<USER>\.nuget\packages\system.io.pipelines\6.0.0\lib\net6.0\System.IO.Pipelines.dll
C:\Users\<USER>\.nuget\packages\system.reactive\6.0.0\lib\net6.0\System.Reactive.dll
C:\Users\<USER>\.nuget\packages\tmds.dbus.protocol\0.15.0\lib\net6.0\Tmds.DBus.Protocol.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\cs\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\de\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\es\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\fr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\it\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\ja\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\ko\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\pl\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\ru\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\tr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\de\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\es\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\it\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\cs\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\de\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\es\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\fr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\it\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\ja\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\ko\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\pl\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\ru\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\tr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\cs\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\de\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\es\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\fr\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\it\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\ja\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\ko\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\pl\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\ru\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\tr\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\.nuget\packages\avalonia.angle.windows.natives\2.1.0.2023020321\runtimes\win-arm64\native\av_libglesv2.dll
C:\Users\<USER>\.nuget\packages\avalonia.angle.windows.natives\2.1.0.2023020321\runtimes\win-x64\native\av_libglesv2.dll
C:\Users\<USER>\.nuget\packages\avalonia.angle.windows.natives\2.1.0.2023020321\runtimes\win-x86\native\av_libglesv2.dll
C:\Users\<USER>\.nuget\packages\avalonia.native\11.0.10\runtimes\osx\native\libAvaloniaNative.dylib
C:\Users\<USER>\.nuget\packages\harfbuzzsharp.nativeassets.linux\7.3.0\runtimes\linux-arm\native\libHarfBuzzSharp.so
C:\Users\<USER>\.nuget\packages\harfbuzzsharp.nativeassets.linux\7.3.0\runtimes\linux-arm64\native\libHarfBuzzSharp.so
C:\Users\<USER>\.nuget\packages\harfbuzzsharp.nativeassets.linux\7.3.0\runtimes\linux-musl-x64\native\libHarfBuzzSharp.so
C:\Users\<USER>\.nuget\packages\harfbuzzsharp.nativeassets.linux\7.3.0\runtimes\linux-x64\native\libHarfBuzzSharp.so
C:\Users\<USER>\.nuget\packages\harfbuzzsharp.nativeassets.macos\7.3.0\runtimes\osx\native\libHarfBuzzSharp.dylib
C:\Users\<USER>\.nuget\packages\harfbuzzsharp.nativeassets.win32\7.3.0\runtimes\win-arm64\native\libHarfBuzzSharp.dll
C:\Users\<USER>\.nuget\packages\harfbuzzsharp.nativeassets.win32\7.3.0\runtimes\win-x64\native\libHarfBuzzSharp.dll
C:\Users\<USER>\.nuget\packages\harfbuzzsharp.nativeassets.win32\7.3.0\runtimes\win-x86\native\libHarfBuzzSharp.dll
C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.linux\2.88.7\runtimes\linux-arm\native\libSkiaSharp.so
C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.linux\2.88.7\runtimes\linux-arm64\native\libSkiaSharp.so
C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.linux\2.88.7\runtimes\linux-musl-x64\native\libSkiaSharp.so
C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.linux\2.88.7\runtimes\linux-x64\native\libSkiaSharp.so
C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.macos\2.88.7\runtimes\osx\native\libSkiaSharp.dylib
C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.win32\2.88.7\runtimes\win-arm64\native\libSkiaSharp.dll
C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.win32\2.88.7\runtimes\win-x64\native\libSkiaSharp.dll
C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.win32\2.88.7\runtimes\win-x86\native\libSkiaSharp.dll
