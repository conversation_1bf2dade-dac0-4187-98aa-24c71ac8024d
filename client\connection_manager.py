import websockets
import logging

logger = logging.getLogger(__name__)

class ConnectionManager:
    def __init__(self, server_url: str, client_id: str):
        self.server_url = server_url
        self.client_id = client_id
        self.connections = {}
        self.running = True

    async def create_connection(self, module_name: str):
        """为指定模块创建WebSocket连接"""
        try:
            websocket = await websockets.connect(
                self.server_url + f"/ws/{module_name}/{self.client_id}",
                max_size=100 * 1024 * 1024
            )
            self.connections[module_name] = websocket
            logger.info(f"模块 {module_name} 连接已创建")
            return websocket
        except Exception as e:
            logger.error(f"创建 {module_name} 连接失败: {e}")
            return None

    async def get_connection(self, module_name: str):
        """获取或创建模块的连接"""
        if module_name not in self.connections:
            return await self.create_connection(module_name)
        return self.connections[module_name]

    async def close_connection(self, module_name: str):
        """关闭指定模块的连接"""
        if module_name in self.connections:
            try:
                await self.connections[module_name].close()
                del self.connections[module_name]
                logger.info(f"模块 {module_name} 连接已关闭")
            except Exception as e:
                logger.error(f"关闭 {module_name} 连接失败: {e}")

    async def close_all(self):
        """关闭所有连接"""
        for module_name in list(self.connections.keys()):
            await self.close_connection(module_name)
        self.running = False 