C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.Base.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.colorpicker\11.0.10\lib\net6.0\Avalonia.Controls.ColorPicker.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.datagrid\11.0.10\lib\net6.0\Avalonia.Controls.DataGrid.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.Controls.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.DesignerSupport.dll
C:\Users\<USER>\.nuget\packages\avalonia.desktop\11.0.10\lib\net6.0\Avalonia.Desktop.dll
C:\Users\<USER>\.nuget\packages\avalonia.diagnostics\11.0.10\lib\net6.0\Avalonia.Diagnostics.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.Dialogs.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.dll
C:\Users\<USER>\.nuget\packages\avalonia.fonts.inter\11.0.10\lib\net6.0\Avalonia.Fonts.Inter.dll
C:\Users\<USER>\.nuget\packages\avalonia.freedesktop\11.0.10\lib\net6.0\Avalonia.FreeDesktop.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.Markup.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.Markup.Xaml.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.Metal.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.MicroCom.dll
C:\Users\<USER>\.nuget\packages\avalonia.native\11.0.10\lib\net6.0\Avalonia.Native.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.0.10\ref\net6.0\Avalonia.OpenGL.dll
C:\Users\<USER>\.nuget\packages\avalonia.reactiveui\11.0.10\lib\net6.0\Avalonia.ReactiveUI.dll
C:\Users\<USER>\.nuget\packages\avalonia.remote.protocol\11.0.10\lib\net6.0\Avalonia.Remote.Protocol.dll
C:\Users\<USER>\.nuget\packages\avalonia.skia\11.0.10\lib\net6.0\Avalonia.Skia.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.fluent\11.0.10\lib\net6.0\Avalonia.Themes.Fluent.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.simple\11.0.10\lib\net6.0\Avalonia.Themes.Simple.dll
C:\Users\<USER>\.nuget\packages\avalonia.win32\11.0.10\lib\net6.0\Avalonia.Win32.dll
C:\Users\<USER>\.nuget\packages\avalonia.x11\11.0.10\lib\net6.0\Avalonia.X11.dll
C:\Users\<USER>\.nuget\packages\dynamicdata\8.3.27\lib\net8.0\DynamicData.dll
C:\Users\<USER>\.nuget\packages\harfbuzzsharp\7.3.0\lib\net6.0\HarfBuzzSharp.dll
C:\Users\<USER>\.nuget\packages\microcom.runtime\0.11.0\lib\net5.0\MicroCom.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.scripting\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.scripting.common\3.8.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\Microsoft.CSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\Microsoft.VisualBasic.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\Microsoft.Win32.Registry.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\mscorlib.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\netstandard.dll
C:\Users\<USER>\.nuget\packages\reactiveui\19.5.41\lib\net8.0\ReactiveUI.dll
C:\Users\<USER>\.nuget\packages\reactiveui.fody\19.5.41\lib\net8.0\ReactiveUI.Fody.Helpers.dll
C:\Users\<USER>\.nuget\packages\serilog\3.1.1\lib\net7.0\Serilog.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.console\5.0.1\lib\net7.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.file\5.0.0\lib\net5.0\Serilog.Sinks.File.dll
C:\Users\<USER>\.nuget\packages\skiasharp\2.88.7\lib\net6.0\SkiaSharp.dll
C:\Users\<USER>\.nuget\packages\splat\14.8.12\lib\net8.0\Splat.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.AppContext.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Buffers.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Collections.Concurrent.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Collections.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Collections.Immutable.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Collections.NonGeneric.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Collections.Specialized.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ComponentModel.Annotations.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ComponentModel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ComponentModel.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Configuration.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Console.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Data.Common.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Data.DataSetExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Data.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.Contracts.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.Debug.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.Process.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.Tools.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Diagnostics.Tracing.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Drawing.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Drawing.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Dynamic.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Formats.Asn1.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Formats.Tar.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Globalization.Calendars.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Globalization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Globalization.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.Compression.Brotli.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.Compression.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.FileSystem.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.IsolatedStorage.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\.nuget\packages\system.io.pipelines\6.0.0\lib\net6.0\System.IO.Pipelines.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.Pipes.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Linq.Expressions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Linq.Parallel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Linq.Queryable.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Memory.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Http.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Http.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.HttpListener.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Mail.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.NameResolution.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.NetworkInformation.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Ping.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Quic.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Requests.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Security.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.ServicePoint.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.Sockets.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.WebClient.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.WebProxy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.WebSockets.Client.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Net.WebSockets.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Numerics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Numerics.Vectors.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ObjectModel.dll
C:\Users\<USER>\.nuget\packages\system.reactive\6.0.0\lib\net6.0\System.Reactive.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.Emit.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.Metadata.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Resources.Reader.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Resources.ResourceManager.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Resources.Writer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Handles.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.InteropServices.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Intrinsics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Loader.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Numerics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Serialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Claims.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Cryptography.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Principal.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.Principal.Windows.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Security.SecureString.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ServiceModel.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ServiceProcess.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Text.Encoding.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Text.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Text.RegularExpressions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.Channels.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.Overlapped.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.Tasks.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.Thread.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.ThreadPool.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Threading.Timer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Transactions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Transactions.Local.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.ValueTuple.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Web.HttpUtility.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Windows.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.ReaderWriter.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.Serialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.XDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.XmlDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.XmlSerializer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.XPath.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\.nuget\packages\tmds.dbus.protocol\0.15.0\lib\net6.0\Tmds.DBus.Protocol.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\8.0.17\ref\net8.0\WindowsBase.dll
